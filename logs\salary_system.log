2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 16:47:23.972 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 16:47:23.987 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 16:47:25.291 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 16:47:25.291 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 16:47:25.291 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 16:47:25.291 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 16:47:25.318 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 16:47:25.323 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 16:47:25.327 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.328 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 16:47:25.329 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 16:47:25.329 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 16:47:25.329 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 16:47:25.337 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 16:47:25.612 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.614 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 16:47:25.626 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 16:47:25.627 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 16:47:25.628 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 16:47:25.639 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 16:47:25.651 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 16:47:25.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 16:47:25.697 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 16:47:25.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 16:47:25.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 16:47:25.709 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 16:47:25.728 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 95个展开项
2025-06-26 16:47:25.749 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-26 16:47:25.750 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 16:47:25.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 16:47:25.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 16:47:25.828 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:25.829 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 16:47:25.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 16:47:25.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:25.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:25.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.83ms
2025-06-26 16:47:25.856 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:25.882 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 16:47:25.949 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 16:47:25.996 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 16:47:25.997 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 16:47:25.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 16:47:26.000 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:26.095 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共56个表的映射
2025-06-26 16:47:26.095 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:26.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:26.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:26.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.02ms
2025-06-26 16:47:26.099 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:26.099 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:26.099 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:26.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.53ms
2025-06-26 16:47:26.102 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:26.102 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:26.103 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 16:47:26.432 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 16:47:26.437 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 16:47:26.438 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 16:47:26.507 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 16:47:26.520 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 16:47:26.523 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 16:47:26.997 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 16:47:26.998 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 8 个月份
2025-06-26 16:47:26.999 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 16:47:27.000 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 16:47:27.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 16:47:27.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 16:47:27.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 16:47:27.002 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 16:47:27.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 16:47:27.003 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 16:47:27.004 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 20 个月份
2025-06-26 16:47:27.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:47:27.005 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 4月']
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年
2025-06-26 16:47:32.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:32.139 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:32.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:32.139 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:32.139 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年']
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 16:47:34.078 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:34.078 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:34.078 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:47:34.078 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:34.078 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:47:35.753 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_a_grade_employees 第1页数据，每页50条
2025-06-26 16:47:35.753 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:47:35.753 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_a_grade_employees: 7 个字段重命名
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:47:35.753 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:47:35.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:47:35.769 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:47:35.769 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:47:35.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:47:35.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:47:35.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 61.54ms
2025-06-26 16:47:35.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:47:35.833 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:47:38.366 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:47:38.366 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:01.389 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 id_card 在表 salary_data_2026_07_a_grade_employees 中不存在，创建新字段映射
2025-06-26 16:48:01.398 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_07_a_grade_employees.id_card -> 身份证
2025-06-26 16:48:01.398 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: id_card -> 身份证
2025-06-26 16:48:01.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:01.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:48:01.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列3, id_card -> 身份证
2025-06-26 16:48:17.326 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 姓名 在表 salary_data_2026_07_a_grade_employees 中不存在，创建新字段映射
2025-06-26 16:48:17.341 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_07_a_grade_employees.姓名 -> 姓名1
2025-06-26 16:48:17.341 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: 姓名 -> 姓名1
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:48:17.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, 姓名 -> 姓名1
2025-06-26 16:48:28.405 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:48:28.405 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:28.405 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:28.420 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:28.448 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 16:48:28.452 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_pension_employees: 7 个字段重命名
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:28.452 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:28.452 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:48:28.485 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 16:48:28.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:48:28.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:28.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:48:28.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 105.47ms
2025-06-26 16:48:28.558 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 16:48:43.126 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 16:48:43.126 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:43.142 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 16:48:43.142 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_retired_employees: 5 个字段重命名
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:43.142 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:43.142 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 16:48:43.142 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 16:48:43.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 16:48:43.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 16:48:43.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:43.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 16:48:43.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 86.06ms
2025-06-26 16:48:43.231 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 16:48:45.004 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:48:45.004 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:51.420 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表']
2025-06-26 16:48:51.421 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:51.422 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:51.422 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:48:51.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 16:48:51.423 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:48:51.424 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 16:48:51.426 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 16:48:51.426 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:48:51.427 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_pension_employees: 7 个字段重命名
2025-06-26 16:48:51.429 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:48:51.429 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:51.431 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:51.431 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:48:51.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 16:48:51.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:48:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:51.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:48:51.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 6.57ms
2025-06-26 16:48:51.439 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:48:53.335 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:48:53.336 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表']
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 16:48:59.828 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_active_employees，第1页，每页50条
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_07_active_employees 第1页
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_active_employees 第1页数据，每页50条
2025-06-26 16:48:59.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:48:59.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_07_active_employees: 7 个字段重命名
2025-06-26 16:48:59.828 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 16:48:59.845 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:48:59.845 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_active_employees 无字段偏好设置，显示所有字段
2025-06-26 16:48:59.845 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:48:59.860 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:48:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:48:59.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 57.20ms
2025-06-26 16:48:59.918 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:48:59.919 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年']
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:49:03.220 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2794 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2666 | 表 salary_data_2026_07_a_grade_employees 无需字段重命名
2025-06-26 16:49:03.220 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 16:49:03.241 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:49:03.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:49:03.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 40.99ms
2025-06-26 16:49:03.304 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:03.305 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:03.311 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年']
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月
2025-06-26 16:49:25.626 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:25.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:25.642 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:25.642 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:25.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.67ms
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:25.658 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:25.658 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 6月', '工资表 > 2026年 > 7月']
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 6月 > A岗职工
2025-06-26 16:49:27.286 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_06_a_grade_employees，第1页，每页50条
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_06_a_grade_employees 第1页
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 6月 > A岗职工
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_06_a_grade_employees 第1页数据，每页50条
2025-06-26 16:49:27.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:49:27.286 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2662 | 应用字段映射到表 salary_data_2026_06_a_grade_employees: 7 个字段重命名
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:49:27.286 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:49:27.303 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_06_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:49:27.303 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_06_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:27.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:27.317 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_06_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:49:27.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:49:27.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:49:27.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 29.90ms
2025-06-26 16:49:27.333 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:27.333 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:29.259 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:49:29.259 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:49:43.410 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 6月', '工资表 > 2026年 > 7月']
2025-06-26 16:49:43.411 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月
2025-06-26 16:49:43.412 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:43.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:43.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:43.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:43.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 18.65ms
2025-06-26 16:49:43.432 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:43.434 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:43.435 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 16:49:43.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 16:49:43.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 16:49:43.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 16:49:43.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 4.71ms
2025-06-26 16:49:43.445 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 16:49:43.448 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:43.450 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月
2025-06-26 16:49:44.474 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:44.474 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 16:49:44.475 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:44.476 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(62条)，启用分页模式
2025-06-26 16:49:44.477 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_a_grade_employees，第1页，每页50条
2025-06-26 16:49:44.477 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_a_grade_employees 第1页
2025-06-26 16:49:44.478 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 16:49:44.478 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_a_grade_employees 第1页数据，每页50条
2025-06-26 16:49:44.479 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:49:44.481 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 16:49:44.482 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:49:44.483 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_a_grade_employees 没有保存的字段映射信息
2025-06-26 16:49:44.484 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 16:49:44.486 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 16:49:44.497 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:49:44.498 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:44.499 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2696 | 应用表 salary_data_2026_01_a_grade_employees 的字段偏好: 7个字段 ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary']
2025-06-26 16:49:44.501 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:49:44.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:49:44.506 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 16:49:44.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:49:44.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 7 列
2025-06-26 16:49:44.545 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 41.81ms
2025-06-26 16:49:44.546 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:49:44.547 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 16:49:46.230 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 16:49:46.230 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 16:49:53.999 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(2条)，使用普通模式
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 离休人员
2025-06-26 16:49:53.999 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:49:53.999 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_retired_employees 获取数据...
2025-06-26 16:49:54.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_retired_employees 获取 2 行数据。
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_retired_employees 没有保存的字段映射信息
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:49:54.015 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:54.015 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 16:49:54.015 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 16:49:54.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 16:49:54.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 79.73ms
2025-06-26 16:49:54.095 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 16:49:56.001 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2760 | 数据量小(13条)，使用普通模式
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 16:49:56.001 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2919 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 16:49:56.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_pension_employees 获取数据...
2025-06-26 16:49:56.001 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_pension_employees 获取 13 行数据。
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2926 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_pension_employees 没有保存的字段映射信息
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2931 | 应用字段映射后的表头: ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 16:49:56.016 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 16:49:56.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 16:49:56.016 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 16:50:04.286 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:203 | 表 salary_data_2026_01_pension_employees 配置不存在，创建新的字段映射配置
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 employee_name 在表 salary_data_2026_01_pension_employees 中不存在，创建新字段映射
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_01_pension_employees.employee_name -> 姓名
2025-06-26 16:50:04.301 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: employee_name -> 姓名
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:50:04.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, employee_name -> 姓名
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年 > 6月']
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2712 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:50:12.209 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2757 | 数据量大(1396条)，启用分页模式
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2828 | 缓存未命中，从数据库加载: salary_data_2026_01_active_employees 第1页
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 16:50:12.209 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_active_employees 第1页数据，每页50条
2025-06-26 16:50:12.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:50:12.225 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2643 | 表 salary_data_2026_01_active_employees 没有保存的字段映射信息
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 16:50:12.225 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2858 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 16:50:12.244 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:50:12.251 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:12.264 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:50:12.268 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2702 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 16:50:12.295 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 16:50:12.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 16:50:12.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:12.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:12.338 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.37ms
2025-06-26 16:50:12.338 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 16:50:12.339 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 16:50:22.072 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:203 | 表 salary_data_2026_01_active_employees 配置不存在，创建新的字段映射配置
2025-06-26 16:50:22.072 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:222 | 字段 employee_name 在表 salary_data_2026_01_active_employees 中不存在，创建新字段映射
2025-06-26 16:50:22.088 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:257 | 字段映射更新成功: salary_data_2026_01_active_employees.employee_name -> 姓名
2025-06-26 16:50:22.088 | INFO     | src.modules.data_import.header_edit_manager:show_edit_dialog:115 | 字段编辑成功: employee_name -> 姓名
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_refresh_header_display:4008 | 表头显示刷新完成，更新了 1 个表头
2025-06-26 16:50:22.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_double_clicked:3957 | 表头编辑成功: 列2, employee_name -> 姓名
2025-06-26 16:50:33.380 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:33.381 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:33.381 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:33.382 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 16:50:33.384 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 16:50:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:33.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:33.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:33.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 21.86ms
2025-06-26 16:50:33.409 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 16:50:33.410 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 16:50:43.457 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:43.457 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:43.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第3页, 每页50条
2025-06-26 16:50:43.457 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:43.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:43.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.34ms
2025-06-26 16:50:43.489 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 16
2025-06-26 16:50:43.489 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-26 16:50:51.838 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:50:51.854 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:50:51.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第4页, 每页50条
2025-06-26 16:50:51.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第4页数据: 50 行，总计1396行
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:50:51.854 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:50:51.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.65ms
2025-06-26 16:50:51.870 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第4页, 显示50条记录，字段数: 16
2025-06-26 16:50:51.870 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 4
2025-06-26 16:51:00.046 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:51:00.046 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:51:00.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:51:00.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:51:00.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:51:00.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.32ms
2025-06-26 16:51:00.077 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 16:51:00.077 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 16:51:14.096 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 16:51:14.096 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 16:51:14.096 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 16:51:14.096 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 16:51:14.111 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 16:51:14.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 16:51:14.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 22.18ms
2025-06-26 16:51:14.148 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 16:51:14.150 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_refresh:627 | 本地分页数据刷新完成
2025-06-26 16:51:28.684 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 17:21:28.879 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:21:28.880 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:21:28.880 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:21:28.880 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:21:28.881 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:21:28.881 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:21:29.743 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.746 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.751 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.752 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'salary_data_2025_03_test' 生成字段映射，共 9 个字段
2025-06-26 17:21:29.759 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 17:21:29.769 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.773 | INFO     | src.modules.data_import.auto_field_mapping_generator:validate_mapping_keys:166 | 字段映射验证完成:
2025-06-26 17:21:29.774 | INFO     | src.modules.data_import.auto_field_mapping_generator:validate_mapping_keys:170 | 验证结果: 原始 4 个字段，验证后 6 个字段
2025-06-26 17:21:29.775 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:21:29.776 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:84 | 开始为表 'test_table' 生成字段映射，共 4 个字段
2025-06-26 17:21:29.776 | INFO     | src.modules.data_import.auto_field_mapping_generator:generate_mapping:131 | 字段映射生成完成，详细信息:
2025-06-26 17:24:00.400 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:24:00.400 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:24:00.401 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:24:00.401 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:24:00.402 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:24:00.402 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:24:00.704 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:24:00.705 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 7 个字段
2025-06-26 17:24:00.708 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:24:00.709 | ERROR    | src.modules.data_import.config_sync_manager:_load_config_file:501 | 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-06-26 17:24:00.710 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_01_demo
2025-06-26 17:24:00.735 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:24:00.735 | ERROR    | src.modules.data_import.config_sync_manager:_load_config_file:501 | 加载配置文件失败: Expecting value: line 1 column 1 (char 0)
2025-06-26 17:24:00.735 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: demo_table
2025-06-26 17:24:00.735 | INFO     | src.modules.data_import.config_sync_manager:update_single_field_mapping:309 | 字段映射更新成功: demo_table.employee_name -> 员工姓名
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:32:23.049 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:32:23.049 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:32:24.419 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 17:32:24.419 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 17:32:24.419 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 17:32:24.419 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 17:32:24.450 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 17:32:24.450 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 17:32:24.450 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:24.450 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 17:32:24.450 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 17:32:24.450 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 17:32:24.450 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 17:32:24.450 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 17:32:24.713 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 17:32:24.713 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:32:24.713 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:32:24.713 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 17:32:24.744 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 17:32:24.744 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 17:32:24.787 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 17:32:24.789 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 17:32:24.799 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 17:32:24.800 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 17:32:24.802 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 17:32:24.837 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 97个展开项
2025-06-26 17:32:24.939 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '异动人员表 > 2024年', '工资表', '异动人员表']
2025-06-26 17:32:24.941 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 17:32:25.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 17:32:25.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 17:32:25.036 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:32:25.036 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 17:32:25.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 17:32:25.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:25.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:25.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.62ms
2025-06-26 17:32:25.045 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:25.053 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 17:32:25.086 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 17:32:25.122 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:32:25.123 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:32:25.123 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 17:32:25.124 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 17:32:25.124 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 17:32:25.124 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:32:25.220 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共58个表的映射
2025-06-26 17:32:25.220 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:25.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:25.222 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:25.222 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.58ms
2025-06-26 17:32:25.223 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:25.224 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:25.224 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:25.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:25.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:25.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.15ms
2025-06-26 17:32:25.229 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:25.231 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:25.231 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 17:32:25.606 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 17:32:25.613 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 17:32:25.614 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 17:32:25.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 17:32:25.617 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 17:32:25.618 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 17:32:26.075 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 8 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 20 个月份
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 17:32:26.075 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 17:32:37.592 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月', '异动人员表 > 2025年']
2025-06-26 17:32:37.592 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年
2025-06-26 17:32:37.592 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:37.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.67ms
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:37.608 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:37.608 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:37.608 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:37.608 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年 > 4月']
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 17:32:41.724 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:41.724 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:41.724 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 17:32:41.724 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:32:41.724 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:41.739 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:32:43.456 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:32:43.456 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_a_grade_employees 第1页数据，每页50条
2025-06-26 17:32:43.456 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:32:43.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 8 个字段重命名
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 17:32:43.472 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:32:43.472 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:32:43.472 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:32:43.487 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:32:43.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:32:43.524 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.84ms
2025-06-26 17:32:43.534 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:32:43.535 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:32:49.203 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:32:49.203 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:33:03.870 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:03.871 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:33:03.872 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:03.872 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:33:03.872 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:33:03.873 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:33:03.873 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 17:33:03.875 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 17:33:03.876 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:33:03.877 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 7 个字段重命名
2025-06-26 17:33:03.878 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:33:03.880 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:03.882 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:33:03.884 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:33:04.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 17:33:04.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:33:04.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:04.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:33:04.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 215.96ms
2025-06-26 17:33:04.102 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:33:14.482 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:33:14.482 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 17:33:14.482 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:33:14.482 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:33:14.482 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:33:14.482 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:14.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:33:14.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 32.28ms
2025-06-26 17:33:14.514 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:33:17.559 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:33:17.559 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 7月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 17:33:22.987 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_active_employees，第1页，每页50条
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_07_active_employees 第1页
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 全部在职人员
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_07_active_employees 第1页数据，每页50条
2025-06-26 17:33:22.987 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:33:22.987 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 17:33:22.987 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 7 个字段重命名
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 17:33:23.003 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:33:23.003 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_active_employees 无字段偏好设置，显示所有字段
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 50
2025-06-26 17:33:23.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:33:23.021 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:33:23.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:23.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:33:23.058 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 54.57ms
2025-06-26 17:33:23.058 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:33:23.058 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 17:33:27.139 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:33:27.139 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:27.139 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:33:27.139 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:33:27.139 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:33:27.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:33:27.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:33:27.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:33:27.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:33:27.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 93.82ms
2025-06-26 17:33:27.263 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 17:33:27.263 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月
2025-06-26 17:33:48.766 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 17:33:48.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 15.68ms
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:33:48.782 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:48.782 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 7 个表头
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 0.00ms
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:33:48.782 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:33:48.782 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月
2025-06-26 17:33:57.239 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_action_triggered:2295 | 菜单动作触发: reset_headers
2025-06-26 17:33:59.146 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:33:59.146 | INFO     | src.gui.prototype.prototype_main_window:_reload_current_table_data:3615 | 重新加载表格数据: salary_data_2026_07_active_employees
2025-06-26 17:33:59.146 | ERROR    | src.gui.prototype.prototype_main_window:_reload_current_table_data:3633 | 重新加载当前表格数据失败: 'PaginationWidget' object has no attribute 'get_current_page'
2025-06-26 17:33:59.146 | INFO     | src.gui.prototype.prototype_main_window:_reset_headers_to_default:3515 | 表头已重置为默认显示: salary_data_2026_07_active_employees
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2025年', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年', '工资表']
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:13.393 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:34:13.393 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 17:34:13.409 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:13.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:34:13.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 16.17ms
2025-06-26 17:34:13.425 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:34:13.425 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:34:13.425 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2026年']
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:34:17.100 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 离休人员
2025-06-26 17:34:17.100 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:34:17.100 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_retired_employees 获取数据...
2025-06-26 17:34:17.120 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_retired_employees 获取 2 行数据。
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '离休\n补贴', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:34:17.120 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:17.120 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:34:17.120 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:17.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:34:17.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 42.60ms
2025-06-26 17:34:17.162 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:34:20.025 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:34:20.025 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2025年', '工资表 > 2026年 > 7月 > 退休人员']
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:34:23.700 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > 退休人员
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:34:23.700 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_07_pension_employees 获取数据...
2025-06-26 17:34:23.700 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_07_pension_employees 获取 13 行数据。
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 7 个字段重命名
2025-06-26 17:34:23.700 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['序号', '人员代码', '姓名', 'id_card', '部门', '人员类别代码', 'basic_salary', 'performance_bonus', 'overtime_pay', '住房补贴', 'deduction', '应发合计', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:34:23.716 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:34:23.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 0.00ms
2025-06-26 17:34:23.716 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:34:25.289 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:34:25.289 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:34:42.763 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:34:42.764 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:42.764 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:34:42.765 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:34:42.766 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:34:42.767 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:34:42.767 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 17:34:42.768 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 17:34:42.769 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:34:42.770 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:34:42.770 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:34:42.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 17:34:42.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:34:42.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:34:42.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:34:42.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 18.71ms
2025-06-26 17:34:42.791 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:34:42.793 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:34:42.801 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:34:44.647 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:34:44.647 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:35:02.455 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:35:02.455 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:35:02.455 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:35:02.455 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_07_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:35:02.455 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_07_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:35:02.470 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:35:02.487 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 32.59ms
2025-06-26 17:35:02.488 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:35:02.488 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:35:17.364 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 17:35:17.364 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2026年 > 7月 > A岗职工。打开导入对话框。
2025-06-26 17:35:17.364 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:35:17.364 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:35:17.364 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 17:35:17.364 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 17:35:17.364 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:35:17.400 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 80 个匹配类型 'salary_data' 的表
2025-06-26 17:35:17.444 | INFO     | src.gui.dialogs:_init_field_mapping:1925 | 初始化字段映射表格: 17 个默认字段
2025-06-26 17:35:17.578 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 17:35:17.579 | INFO     | src.gui.dialogs:_apply_default_settings:2141 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-06-26 17:35:17.579 | INFO     | src.gui.dialogs:_setup_tooltips:2396 | 工具提示设置完成
2025-06-26 17:35:17.580 | INFO     | src.gui.dialogs:_setup_shortcuts:2435 | 快捷键设置完成
2025-06-26 17:35:17.580 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-26 17:35:21.023 | INFO     | src.gui.dialogs:_on_target_changed:2080 | 目标位置已更新: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:35:33.366 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:35:35.046 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:35:35.047 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: A岗职工 -> A岗职工
2025-06-26 17:35:35.048 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2176 | 根据人员类别 'A岗职工' 自动选择工作表: A岗职工
2025-06-26 17:35:47.904 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 21列, 20行
2025-06-26 17:35:59.994 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.214 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.217 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.217 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.217 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.421 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.421 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:36:00.436 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.436 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.436 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:00.577 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-26 17:36:00.577 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:00.580 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-26 17:36:00.581 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-26 17:36:00.581 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-26 17:36:00.581 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 17:36:00.582 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 12 个字段
2025-06-26 17:36:00.591 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_retired_employees
2025-06-26 17:36:00.592 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_retired_employees 生成标准化字段映射: 12 个字段
2025-06-26 17:36:00.592 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-26 17:36:00.597 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-26 17:36:00.599 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-26 17:36:00.599 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_retired_employees 不存在，将根据模板创建...
2025-06-26 17:36:00.615 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。
2025-06-26 17:36:00.628 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.644 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.647 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:00.794 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-26 17:36:00.854 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:00.854 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-26 17:36:00.870 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-26 17:36:00.870 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-26 17:36:00.870 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 17:36:00.870 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 21 个字段
2025-06-26 17:36:00.893 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_pension_employees
2025-06-26 17:36:00.917 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_pension_employees 生成标准化字段映射: 21 个字段
2025-06-26 17:36:00.919 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-26 17:36:00.926 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-26 17:36:00.928 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:36:00.929 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_pension_employees 不存在，将根据模板创建...
2025-06-26 17:36:00.949 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。
2025-06-26 17:36:00.956 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:00.966 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:00.967 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-26 17:36:01.105 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-26 17:36:01.105 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-26 17:36:01.105 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 23 个字段
2025-06-26 17:36:01.121 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_active_employees
2025-06-26 17:36:01.121 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_active_employees 生成标准化字段映射: 23 个字段
2025-06-26 17:36:01.121 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-26 17:36:01.121 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-26 17:36:01.136 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:36:01.138 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_active_employees 不存在，将根据模板创建...
2025-06-26 17:36:01.178 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。
2025-06-26 17:36:01.180 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:36:01.180 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:36:01.181 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:36:01.356 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-26 17:36:01.358 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:36:01.359 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-26 17:36:01.360 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-26 17:36:01.361 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-26 17:36:01.361 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-26 17:36:01.363 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 17 个字段
2025-06-26 17:36:01.372 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2026_08_a_grade_employees
2025-06-26 17:36:01.372 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2026_08_a_grade_employees 生成标准化字段映射: 17 个字段
2025-06-26 17:36:01.373 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet A岗职工 存在 2 个验证错误
2025-06-26 17:36:01.376 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet A岗职工 数据处理完成: 62 行
2025-06-26 17:36:01.377 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:36:01.379 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2026_08_a_grade_employees 不存在，将根据模板创建...
2025-06-26 17:36:01.382 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。
2025-06-26 17:36:01.382 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-26 17:36:01.398 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-08', 'data_description': '2026年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 8月 > A岗职工'}
2025-06-26 17:36:01.403 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2026_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2026_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2026_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2026_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2026_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2026_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2026-08', 'data_description': '2026年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2026年 > 8月 > A岗职工'}
2025-06-26 17:36:01.429 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '工资表 > 2026年 > 8月 > A岗职工'
2025-06-26 17:36:01.430 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:01.433 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: ['工资表', '2026年', '8月', 'A岗职工']
2025-06-26 17:36:01.433 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷新导航面板
2025-06-26 17:36:01.433 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-26 17:36:01.438 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 17:36:01.438 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 17:36:01.446 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:36:01.446 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:36:01.447 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:36:01.448 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:36:01.448 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_07_a_grade_employees，第1页，每页50条
2025-06-26 17:36:01.449 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_07_a_grade_employees 第1页
2025-06-26 17:36:01.450 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 17:36:01.451 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-26 17:36:01.453 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:01.455 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_07_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:36:01.455 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:36:01.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 17:36:01.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:36:01.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:01.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:36:01.474 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 17.64ms
2025-06-26 17:36:01.491 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:36:01.492 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:36:01.498 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 7月 > A岗职工
2025-06-26 17:36:01.550 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 84 个匹配类型 'salary_data' 的表
2025-06-26 17:36:01.553 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 17:36:01.553 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 17:36:01.555 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 17:36:01.556 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 17:36:01.559 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 17:36:01.561 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 17:36:01.565 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 17:36:01.569 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 17:36:01.571 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 21 个月份
2025-06-26 17:36:01.572 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 17:36:01.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 17:36:01.577 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.379 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.394 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:36:02.411 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.412 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:36:02.421 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:36:02.421 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_08_a_grade_employees，第1页，每页50条
2025-06-26 17:36:02.422 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_08_a_grade_employees 第1页
2025-06-26 17:36:02.423 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.424 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_08_a_grade_employees 第1页数据，每页50条
2025-06-26 17:36:02.432 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工资表 > 2026年 > 8月 > A岗职工
2025-06-26 17:36:02.432 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:36:02.442 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:36:02.443 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:02.445 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:36:02.446 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 17:36:02.448 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 17:36:02.460 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:36:02.461 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:02.478 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:36:02.485 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:36:02.499 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:36:02.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:36:02.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:36:02.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:02.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:36:02.573 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 69.24ms
2025-06-26 17:36:02.588 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:36:02.589 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:36:39.598 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:36:39.598 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:39.598 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:39.598 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:36:39.598 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:36:39.613 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 30.86ms
2025-06-26 17:36:39.628 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:36:39.628 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:36:45.912 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:36:45.912 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:45.912 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:36:45.912 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 50
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:45.912 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:36:45.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.66ms
2025-06-26 17:36:45.928 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第1页, 显示50条记录，字段数: 16
2025-06-26 17:36:45.928 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 1
2025-06-26 17:36:53.918 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:36:53.918 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:36:53.918 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:36:53.918 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:36:53.918 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:36:53.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:36:53.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:36:53.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:36:53.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:36:54.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 84.29ms
2025-06-26 17:36:54.003 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:36:54.003 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:37:02.997 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:37:02.997 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > 离休人员
2025-06-26 17:37:03.012 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > 离休人员
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:37:03.012 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_08_retired_employees 获取数据...
2025-06-26 17:37:03.012 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_08_retired_employees 获取 2 行数据。
2025-06-26 17:37:03.012 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:37:03.030 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 2 个字段重命名
2025-06-26 17:37:03.035 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:37:03.041 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:03.055 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:37:03.057 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:37:03.061 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:37:03.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 2
2025-06-26 17:37:03.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:37:03.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:03.087 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:37:03.092 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 31.29ms
2025-06-26 17:37:03.093 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:37:05.309 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 17:37:05.309 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 17:37:15.346 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:37:15.347 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > 退休人员
2025-06-26 17:37:15.348 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:37:15.348 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:37:15.349 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > 退休人员
2025-06-26 17:37:15.349 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:37:15.351 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_08_pension_employees 获取数据...
2025-06-26 17:37:15.353 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_08_pension_employees 获取 13 行数据。
2025-06-26 17:37:15.355 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:37:15.357 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 4 个字段重命名
2025-06-26 17:37:15.359 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '津贴', 'deduction', '应发工资', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:37:15.360 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:15.362 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:37:15.363 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:37:15.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 17:37:15.379 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:37:15.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:15.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:37:15.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 22.34ms
2025-06-26 17:37:15.396 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2026年 > 7月 > A岗职工', '工资表 > 2026年 > 7月', '工资表 > 2026年 > 7月 > 退休人员', '工资表 > 2026年 > 7月 > 离休人员', '工资表 > 2025年']
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 8月 > 全部在职人员
2025-06-26 17:37:27.012 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_08_active_employees，第1页，每页50条
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_08_active_employees 第1页
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 8月 > 全部在职人员
2025-06-26 17:37:27.012 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_08_active_employees 第1页数据，每页50条
2025-06-26 17:37:27.012 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:37:27.027 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 17:37:27.027 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 17:37:27.052 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:37:27.052 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:27.065 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:37:27.069 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_08_active_employees 无字段偏好设置，显示所有字段
2025-06-26 17:37:27.091 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:37:27.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 17:37:27.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:37:27.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:27.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:37:27.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.37ms
2025-06-26 17:37:27.131 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:37:27.132 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 17:37:43.302 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:37:43.302 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:37:43.302 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:37:43.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_08_active_employees 分页获取数据: 第28页, 每页50条
2025-06-26 17:37:43.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_08_active_employees 获取第28页数据: 46 行，总计1396行
2025-06-26 17:37:43.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 46
2025-06-26 17:37:43.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(46)自动调整最大可见行数为: 46
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 46 行, 16 列
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 46 行, 最大可见行数: 46, 耗时: 31.38ms
2025-06-26 17:37:43.334 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第28页, 显示46条记录，字段数: 16
2025-06-26 17:37:43.334 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 28
2025-06-26 17:38:13.745 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 17:48:22.393 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:48:22.393 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:48:22.394 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:48:22.394 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:48:22.395 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:48:22.395 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:48:23.741 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 17:48:23.741 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 17:48:23.742 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:48:23.742 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:48:23.742 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 17:48:23.744 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 17:48:23.745 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 17:48:23.755 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 17:48:23.755 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 17:48:23.756 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:48:23.756 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 17:48:23.758 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 17:48:23.760 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 17:48:23.761 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 17:48:23.764 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 17:48:24.177 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 17:48:24.177 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:48:24.180 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:48:24.183 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 17:48:24.220 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 17:48:24.220 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 17:48:24.222 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 17:48:24.253 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 17:48:24.253 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 17:48:24.287 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 17:48:24.323 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 17:48:24.323 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 17:48:24.323 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 17:48:24.337 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 17:48:24.389 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 98个展开项
2025-06-26 17:48:24.396 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '异动人员表', '异动人员表 > 2025年', '工资表', '异动人员表 > 2025年 > 5月']
2025-06-26 17:48:24.397 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 17:48:24.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 17:48:24.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 17:48:24.500 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:48:24.501 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 17:48:24.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 17:48:24.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:48:24.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:48:24.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 10.83ms
2025-06-26 17:48:24.516 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:48:24.541 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 17:48:24.621 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 17:48:24.642 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 17:48:24.644 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:48:24.645 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 17:48:24.645 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 17:48:24.646 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 17:48:24.647 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:48:24.780 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共62个表的映射
2025-06-26 17:48:24.781 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:48:24.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:48:24.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:48:24.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.60ms
2025-06-26 17:48:24.786 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:48:24.786 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:48:24.787 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 17:48:24.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 17:48:24.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 17:48:24.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.05ms
2025-06-26 17:48:24.790 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 17:48:24.790 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:48:24.792 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 17:48:25.310 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 17:48:25.314 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 17:48:25.315 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 17:48:25.322 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 17:48:25.322 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 17:48:25.322 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 17:48:25.367 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 84 个匹配类型 'salary_data' 的表
2025-06-26 17:48:25.522 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 17:48:25.566 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 3 个月份
2025-06-26 17:48:25.566 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 21 个月份
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 17:48:25.576 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 17:48:45.430 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 17:48:45.431 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 06月 > 全部在职人员。打开导入对话框。
2025-06-26 17:48:45.433 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 17:48:45.434 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:48:45.435 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 17:48:45.436 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 17:48:45.442 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 06月 > 全部在职人员
2025-06-26 17:48:45.481 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 84 个匹配类型 'salary_data' 的表
2025-06-26 17:48:45.566 | INFO     | src.gui.dialogs:_init_field_mapping:1925 | 初始化字段映射表格: 17 个默认字段
2025-06-26 17:48:45.704 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 17:48:45.704 | INFO     | src.gui.dialogs:_apply_default_settings:2141 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-06-26 17:48:45.706 | INFO     | src.gui.dialogs:_setup_tooltips:2396 | 工具提示设置完成
2025-06-26 17:48:45.707 | INFO     | src.gui.dialogs:_setup_shortcuts:2435 | 快捷键设置完成
2025-06-26 17:48:45.707 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-26 17:48:54.055 | INFO     | src.gui.dialogs:_on_target_changed:2080 | 目标位置已更新: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:49:02.819 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:49:04.875 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:49:04.878 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-26 17:49:04.878 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2176 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-26 17:49:18.197 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 23列, 20行
2025-06-26 17:49:31.111 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:50:40.989 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:50:52.666 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:50:52.667 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:50:52.667 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:50:52.667 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:50:52.668 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:50:52.668 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:50:52.669 | INFO     | __main__:migrate_all_mappings:206 | 开始字段映射迁移...
2025-06-26 17:50:52.672 | INFO     | __main__:migrate_all_mappings:212 | 已备份原配置文件到: state/data/field_mappings.json.backup_20250626_175052
2025-06-26 17:50:52.674 | INFO     | __main__:migrate_single_table:162 | 表 工资数据表 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.674 | INFO     | __main__:migrate_all_mappings:244 | 表 工资数据表 迁移完成
2025-06-26 17:50:52.674 | INFO     | __main__:migrate_single_table:162 | 表 异动人员表 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.679 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '员工编号' 的标准映射，生成字段名: 员工编号
2025-06-26 17:50:52.679 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '异动类型' 的标准映射，生成字段名: 异动类型
2025-06-26 17:50:52.679 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '异动原因' 的标准映射，生成字段名: 异动原因
2025-06-26 17:50:52.680 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '生效日期' 的标准映射，生成字段名: 生效日期
2025-06-26 17:50:52.680 | INFO     | __main__:migrate_all_mappings:244 | 表 异动人员表 迁移完成
2025-06-26 17:50:52.680 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2025_05_active_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.681 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.682 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2025_05_active_employees 迁移完成
2025-06-26 17:50:52.683 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2018_06_retired_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.683 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本
离休费' 的标准映射，生成字段名: 基本
离休费
2025-06-26 17:50:52.684 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余
津贴' 的标准映射，生成字段名: 结余
津贴
2025-06-26 17:50:52.684 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '生活
补贴' 的标准映射，生成字段名: 生活
补贴
2025-06-26 17:50:52.686 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房
补贴' 的标准映射，生成字段名: 住房
补贴
2025-06-26 17:50:52.686 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业
补贴' 的标准映射，生成字段名: 物业
补贴
2025-06-26 17:50:52.686 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离休
补贴' 的标准映射，生成字段名: 离休
补贴
2025-06-26 17:50:52.686 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:52.687 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:52.687 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2018_06_retired_employees 迁移完成
2025-06-26 17:50:52.687 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2018_06_pension_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.688 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:52.688 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.689 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离退休生活补贴' 的标准映射，生成字段名: 离退休生活补贴
2025-06-26 17:50:52.689 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.690 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.690 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:52.691 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:52.693 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:52.695 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:52.695 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:52.696 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:52.696 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:52.697 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:52.698 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:52.698 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2018_06_pension_employees 迁移完成
2025-06-26 17:50:52.699 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2018_06_active_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.699 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.700 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年薪级工资' 的标准映射，生成字段名: 2025年薪级工资
2025-06-26 17:50:52.700 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.701 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.703 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.704 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '交通补贴' 的标准映射，生成字段名: 交通补贴
2025-06-26 17:50:52.705 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.705 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.706 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:52.710 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '通讯补贴' 的标准映射，生成字段名: 通讯补贴
2025-06-26 17:50:52.710 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.712 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:52.715 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.715 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:52.716 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2018_06_active_employees 迁移完成
2025-06-26 17:50:52.716 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2018_06_a_grade_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.717 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.717 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄工资' 的标准映射，生成字段名: 2025年校龄工资
2025-06-26 17:50:52.718 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.718 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.718 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.719 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年生活补贴' 的标准映射，生成字段名: 2025年生活补贴
2025-06-26 17:50:52.719 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.720 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.720 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:52.720 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:52.720 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2018_06_a_grade_employees 迁移完成
2025-06-26 17:50:52.721 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2017_01_retired_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.721 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本
离休费' 的标准映射，生成字段名: 基本
离休费
2025-06-26 17:50:52.723 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余
津贴' 的标准映射，生成字段名: 结余
津贴
2025-06-26 17:50:52.725 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '生活
补贴' 的标准映射，生成字段名: 生活
补贴
2025-06-26 17:50:52.725 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房
补贴' 的标准映射，生成字段名: 住房
补贴
2025-06-26 17:50:52.726 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业
补贴' 的标准映射，生成字段名: 物业
补贴
2025-06-26 17:50:52.726 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离休
补贴' 的标准映射，生成字段名: 离休
补贴
2025-06-26 17:50:52.726 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:52.727 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:52.727 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2017_01_retired_employees 迁移完成
2025-06-26 17:50:52.729 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2017_01_pension_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.729 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:52.729 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.729 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离退休生活补贴' 的标准映射，生成字段名: 离退休生活补贴
2025-06-26 17:50:52.729 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.856 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.857 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:52.858 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:52.858 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:52.860 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:52.860 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:52.861 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:52.863 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:52.863 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:52.864 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:52.864 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2017_01_pension_employees 迁移完成
2025-06-26 17:50:52.865 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2017_01_active_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.890 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.890 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年薪级工资' 的标准映射，生成字段名: 2025年薪级工资
2025-06-26 17:50:52.891 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.892 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.892 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.895 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '交通补贴' 的标准映射，生成字段名: 交通补贴
2025-06-26 17:50:52.896 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.896 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.897 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:52.897 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '通讯补贴' 的标准映射，生成字段名: 通讯补贴
2025-06-26 17:50:52.898 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.898 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:52.898 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.899 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:52.906 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2017_01_active_employees 迁移完成
2025-06-26 17:50:52.907 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2017_01_a_grade_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.908 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.908 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄工资' 的标准映射，生成字段名: 2025年校龄工资
2025-06-26 17:50:52.908 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.908 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.910 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.916 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年生活补贴' 的标准映射，生成字段名: 2025年生活补贴
2025-06-26 17:50:52.917 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.917 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.917 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2017_01_a_grade_employees 迁移完成
2025-06-26 17:50:52.917 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_02_retired_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.918 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本
离休费' 的标准映射，生成字段名: 基本
离休费
2025-06-26 17:50:52.918 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余
津贴' 的标准映射，生成字段名: 结余
津贴
2025-06-26 17:50:52.919 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '生活
补贴' 的标准映射，生成字段名: 生活
补贴
2025-06-26 17:50:52.919 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房
补贴' 的标准映射，生成字段名: 住房
补贴
2025-06-26 17:50:52.919 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业
补贴' 的标准映射，生成字段名: 物业
补贴
2025-06-26 17:50:52.920 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离休
补贴' 的标准映射，生成字段名: 离休
补贴
2025-06-26 17:50:52.920 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:52.920 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:52.921 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_02_retired_employees 迁移完成
2025-06-26 17:50:52.923 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_02_pension_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.925 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:52.925 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.925 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离退休生活补贴' 的标准映射，生成字段名: 离退休生活补贴
2025-06-26 17:50:52.926 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.926 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.927 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:52.927 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:52.928 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:52.928 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:52.929 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:52.929 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:52.930 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:52.930 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:52.931 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:52.932 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_02_pension_employees 迁移完成
2025-06-26 17:50:52.937 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_02_active_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.938 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.938 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年薪级工资' 的标准映射，生成字段名: 2025年薪级工资
2025-06-26 17:50:52.941 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.942 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.942 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.942 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '交通补贴' 的标准映射，生成字段名: 交通补贴
2025-06-26 17:50:52.944 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.944 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.945 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:52.945 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '通讯补贴' 的标准映射，生成字段名: 通讯补贴
2025-06-26 17:50:52.945 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.946 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:52.946 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.947 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:52.948 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_02_active_employees 迁移完成
2025-06-26 17:50:52.949 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_02_a_grade_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.950 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.950 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄工资' 的标准映射，生成字段名: 2025年校龄工资
2025-06-26 17:50:52.951 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.951 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.951 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.952 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年生活补贴' 的标准映射，生成字段名: 2025年生活补贴
2025-06-26 17:50:52.952 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.953 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.953 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_02_a_grade_employees 迁移完成
2025-06-26 17:50:52.954 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_11_retired_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.954 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本
离休费' 的标准映射，生成字段名: 基本
离休费
2025-06-26 17:50:52.955 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余
津贴' 的标准映射，生成字段名: 结余
津贴
2025-06-26 17:50:52.955 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '生活
补贴' 的标准映射，生成字段名: 生活
补贴
2025-06-26 17:50:52.956 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房
补贴' 的标准映射，生成字段名: 住房
补贴
2025-06-26 17:50:52.956 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业
补贴' 的标准映射，生成字段名: 物业
补贴
2025-06-26 17:50:52.957 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离休
补贴' 的标准映射，生成字段名: 离休
补贴
2025-06-26 17:50:52.958 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:52.959 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:52.959 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_11_retired_employees 迁移完成
2025-06-26 17:50:52.960 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_11_pension_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.961 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:52.963 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.964 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离退休生活补贴' 的标准映射，生成字段名: 离退休生活补贴
2025-06-26 17:50:52.964 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.965 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.965 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:52.966 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:52.966 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:52.967 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:52.967 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:52.968 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:52.969 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:52.969 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:52.971 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:52.971 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_11_pension_employees 迁移完成
2025-06-26 17:50:52.972 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_11_active_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.972 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.972 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年薪级工资' 的标准映射，生成字段名: 2025年薪级工资
2025-06-26 17:50:52.973 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.974 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.974 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.974 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '交通补贴' 的标准映射，生成字段名: 交通补贴
2025-06-26 17:50:52.975 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:52.975 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:52.976 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:52.978 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '通讯补贴' 的标准映射，生成字段名: 通讯补贴
2025-06-26 17:50:52.978 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.979 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:52.980 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.983 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:52.985 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_11_active_employees 迁移完成
2025-06-26 17:50:52.985 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_11_a_grade_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.986 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:52.986 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄工资' 的标准映射，生成字段名: 2025年校龄工资
2025-06-26 17:50:52.986 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:52.986 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:52.986 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:52.988 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年生活补贴' 的标准映射，生成字段名: 2025年生活补贴
2025-06-26 17:50:52.990 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:52.991 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:52.992 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:52.993 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:52.993 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_11_a_grade_employees 迁移完成
2025-06-26 17:50:52.994 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_11 需要迁移，当前格式: excel_column
2025-06-26 17:50:52.994 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2026年岗位工资' 的标准映射，生成字段名: 2026年岗位工资
2025-06-26 17:50:52.994 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_11 迁移完成
2025-06-26 17:50:52.995 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_04_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:52.995 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:52.997 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:52.997 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:52.997 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:52.998 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本离休费' 的标准映射，生成字段名: 基本离休费
2025-06-26 17:50:52.999 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.000 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:53.001 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:53.001 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.002 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_04_retired_employees 迁移完成
2025-06-26 17:50:53.003 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_04_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.006 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.008 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.010 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.011 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.011 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.011 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:53.012 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.012 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:53.012 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:53.013 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:53.014 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:53.014 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:53.014 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:53.015 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:53.015 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:53.016 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:53.016 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.017 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_04_pension_employees 迁移完成
2025-06-26 17:50:53.017 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_04_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.017 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.019 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.020 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.020 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.021 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.023 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.023 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'grade_salary' 的标准映射，生成字段名: grade_salary
2025-06-26 17:50:53.023 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.025 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.025 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.025 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:53.026 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:53.026 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.026 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.026 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.027 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:53.030 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_04_active_employees 迁移完成
2025-06-26 17:50:53.030 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_04_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.031 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.031 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.031 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.031 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.032 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.033 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.033 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄salary' 的标准映射，生成字段名: 2025年校龄salary
2025-06-26 17:50:53.033 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.034 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.034 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.034 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.035 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.035 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.035 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'basic_salary' 的标准映射，生成字段名: basic_salary
2025-06-26 17:50:53.036 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_04_a_grade_employees 迁移完成
2025-06-26 17:50:53.036 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_05_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.037 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.038 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.045 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.045 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.045 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本离休费' 的标准映射，生成字段名: 基本离休费
2025-06-26 17:50:53.045 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.045 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:53.047 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:53.048 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_05_retired_employees 迁移完成
2025-06-26 17:50:53.048 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_05_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.048 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.048 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.049 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.050 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.052 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.055 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:53.055 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.056 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:53.056 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:53.056 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:53.057 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:53.057 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:53.058 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:53.058 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:53.059 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:53.059 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:53.059 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.060 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_05_pension_employees 迁移完成
2025-06-26 17:50:53.061 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_05_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.061 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.062 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.066 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.070 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.070 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.070 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.071 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'grade_salary' 的标准映射，生成字段名: grade_salary
2025-06-26 17:50:53.072 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.074 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.074 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.075 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:53.075 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:53.075 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.076 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.076 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.077 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:53.077 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_05_active_employees 迁移完成
2025-06-26 17:50:53.078 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_05_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.078 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.078 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.079 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.079 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.081 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.083 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.084 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄salary' 的标准映射，生成字段名: 2025年校龄salary
2025-06-26 17:50:53.085 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.085 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.086 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.086 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.086 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.086 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.088 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_05_a_grade_employees 迁移完成
2025-06-26 17:50:53.088 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_06_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.089 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.090 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.090 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.090 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.091 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本离休费' 的标准映射，生成字段名: 基本离休费
2025-06-26 17:50:53.091 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.091 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:53.092 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:53.094 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_06_retired_employees 迁移完成
2025-06-26 17:50:53.094 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_06_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.095 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.095 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.095 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.096 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.096 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.097 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:53.097 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.098 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:53.098 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:53.099 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:53.100 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:53.100 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:53.101 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:53.102 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:53.107 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:53.108 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:53.108 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.108 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_06_pension_employees 迁移完成
2025-06-26 17:50:53.108 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_06_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.110 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.114 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.114 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.115 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.115 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.115 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.116 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'grade_salary' 的标准映射，生成字段名: grade_salary
2025-06-26 17:50:53.116 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.117 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.118 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.118 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:53.119 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:53.119 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.120 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.121 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.121 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:53.122 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_06_active_employees 迁移完成
2025-06-26 17:50:53.125 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_06_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.126 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.128 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.128 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.128 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.131 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.132 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.133 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄salary' 的标准映射，生成字段名: 2025年校龄salary
2025-06-26 17:50:53.133 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.133 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.133 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.134 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.134 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.134 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.135 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_06_a_grade_employees 迁移完成
2025-06-26 17:50:53.136 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_07_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.136 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.136 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.137 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.137 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.138 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本离休费' 的标准映射，生成字段名: 基本离休费
2025-06-26 17:50:53.139 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.140 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:53.140 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:53.141 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_07_retired_employees 迁移完成
2025-06-26 17:50:53.141 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_07_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.144 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.145 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.145 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.145 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.146 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.146 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:53.147 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.148 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:53.149 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:53.149 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:53.150 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:53.151 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:53.151 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:53.152 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:53.152 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:53.152 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:53.154 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.154 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_07_pension_employees 迁移完成
2025-06-26 17:50:53.154 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_07_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.155 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.155 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.155 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.155 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.156 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.156 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.157 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'grade_salary' 的标准映射，生成字段名: grade_salary
2025-06-26 17:50:53.158 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.159 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.159 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.160 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:53.160 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:53.160 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.161 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.162 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.162 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:53.163 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_07_active_employees 迁移完成
2025-06-26 17:50:53.164 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_07_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.164 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id' 的标准映射，生成字段名: id
2025-06-26 17:50:53.165 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.169 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.171 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.171 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position' 的标准映射，生成字段名: position
2025-06-26 17:50:53.175 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'position_salary' 的标准映射，生成字段名: position_salary
2025-06-26 17:50:53.175 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄salary' 的标准映射，生成字段名: 2025年校龄salary
2025-06-26 17:50:53.176 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.176 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'performance' 的标准映射，生成字段名: performance
2025-06-26 17:50:53.176 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.177 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.177 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.177 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'social_insurance' 的标准映射，生成字段名: social_insurance
2025-06-26 17:50:53.177 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'id_card' 的标准映射，生成字段名: id_card
2025-06-26 17:50:53.178 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_07_a_grade_employees 迁移完成
2025-06-26 17:50:53.178 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_08_retired_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:53.180 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.181 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.181 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本
离休费' 的标准映射，生成字段名: 基本
离休费
2025-06-26 17:50:53.182 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余
津贴' 的标准映射，生成字段名: 结余
津贴
2025-06-26 17:50:53.183 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '生活
补贴' 的标准映射，生成字段名: 生活
补贴
2025-06-26 17:50:53.183 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房
补贴' 的标准映射，生成字段名: 住房
补贴
2025-06-26 17:50:53.184 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业
补贴' 的标准映射，生成字段名: 物业
补贴
2025-06-26 17:50:53.184 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离休
补贴' 的标准映射，生成字段名: 离休
补贴
2025-06-26 17:50:53.185 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '护理费' 的标准映射，生成字段名: 护理费
2025-06-26 17:50:53.191 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '合计' 的标准映射，生成字段名: 合计
2025-06-26 17:50:53.191 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_08_retired_employees 迁移完成
2025-06-26 17:50:53.191 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_08_pension_employees 需要迁移，当前格式: excel_column
2025-06-26 17:50:53.194 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.195 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.196 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.196 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.197 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '基本退休费' 的标准映射，生成字段名: 基本退休费
2025-06-26 17:50:53.197 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:53.198 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '离退休生活补贴' 的标准映射，生成字段名: 离退休生活补贴
2025-06-26 17:50:53.198 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:53.198 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:53.198 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '增资预付' 的标准映射，生成字段名: 增资预付
2025-06-26 17:50:53.199 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2016待遇调整' 的标准映射，生成字段名: 2016待遇调整
2025-06-26 17:50:53.199 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2017待遇调整' 的标准映射，生成字段名: 2017待遇调整
2025-06-26 17:50:53.199 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2018待遇调整' 的标准映射，生成字段名: 2018待遇调整
2025-06-26 17:50:53.200 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2019待遇调整' 的标准映射，生成字段名: 2019待遇调整
2025-06-26 17:50:53.200 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2020待遇调整' 的标准映射，生成字段名: 2020待遇调整
2025-06-26 17:50:53.202 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2021待遇调整' 的标准映射，生成字段名: 2021待遇调整
2025-06-26 17:50:53.203 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2022待遇调整' 的标准映射，生成字段名: 2022待遇调整
2025-06-26 17:50:53.206 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2023待遇调整' 的标准映射，生成字段名: 2023待遇调整
2025-06-26 17:50:53.206 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_08_pension_employees 迁移完成
2025-06-26 17:50:53.207 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_08_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.207 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.207 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.209 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.209 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.210 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.211 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:53.213 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年薪级工资' 的标准映射，生成字段名: 2025年薪级工资
2025-06-26 17:50:53.214 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:53.214 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:53.214 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.215 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '交通补贴' 的标准映射，生成字段名: 交通补贴
2025-06-26 17:50:53.215 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '物业补贴' 的标准映射，生成字段名: 物业补贴
2025-06-26 17:50:53.215 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '住房补贴' 的标准映射，生成字段名: 住房补贴
2025-06-26 17:50:53.216 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '车补' 的标准映射，生成字段名: 车补
2025-06-26 17:50:53.217 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '通讯补贴' 的标准映射，生成字段名: 通讯补贴
2025-06-26 17:50:53.217 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:53.217 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '补发' 的标准映射，生成字段名: 补发
2025-06-26 17:50:53.218 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.218 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '代扣代存养老保险' 的标准映射，生成字段名: 代扣代存养老保险
2025-06-26 17:50:53.219 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_08_active_employees 迁移完成
2025-06-26 17:50:53.219 | INFO     | __main__:migrate_single_table:162 | 表 salary_data_2026_08_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:50:53.220 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_id' 的标准映射，生成字段名: employee_id
2025-06-26 17:50:53.220 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'employee_name' 的标准映射，生成字段名: employee_name
2025-06-26 17:50:53.220 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'department' 的标准映射，生成字段名: department
2025-06-26 17:50:53.222 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'allowance' 的标准映射，生成字段名: allowance
2025-06-26 17:50:53.223 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 'total_salary' 的标准映射，生成字段名: total_salary
2025-06-26 17:50:53.223 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年岗位工资' 的标准映射，生成字段名: 2025年岗位工资
2025-06-26 17:50:53.223 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年校龄工资' 的标准映射，生成字段名: 2025年校龄工资
2025-06-26 17:50:53.223 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '结余津贴' 的标准映射，生成字段名: 结余津贴
2025-06-26 17:50:53.225 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年基础性绩效' 的标准映射，生成字段名: 2025年基础性绩效
2025-06-26 17:50:53.225 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '卫生费' 的标准映射，生成字段名: 卫生费
2025-06-26 17:50:53.226 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年生活补贴' 的标准映射，生成字段名: 2025年生活补贴
2025-06-26 17:50:53.226 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '2025年奖励性绩效预发' 的标准映射，生成字段名: 2025年奖励性绩效预发
2025-06-26 17:50:53.227 | WARNING  | __main__:convert_excel_to_db_mapping:116 | 未找到 '借支' 的标准映射，生成字段名: 借支
2025-06-26 17:50:53.228 | INFO     | __main__:migrate_all_mappings:244 | 表 salary_data_2026_08_a_grade_employees 迁移完成
2025-06-26 17:50:53.238 | INFO     | __main__:migrate_all_mappings:269 | 字段映射迁移完成: {'total_tables': 62, 'migrated_tables': 40, 'skipped_tables': 22, 'error_tables': 0, 'migrated_table_names': ['工资数据表', '异动人员表', 'salary_data_2025_05_active_employees', 'salary_data_2018_06_retired_employees', 'salary_data_2018_06_pension_employees', 'salary_data_2018_06_active_employees', 'salary_data_2018_06_a_grade_employees', 'salary_data_2017_01_retired_employees', 'salary_data_2017_01_pension_employees', 'salary_data_2017_01_active_employees', 'salary_data_2017_01_a_grade_employees', 'salary_data_2026_02_retired_employees', 'salary_data_2026_02_pension_employees', 'salary_data_2026_02_active_employees', 'salary_data_2026_02_a_grade_employees', 'salary_data_2026_11_retired_employees', 'salary_data_2026_11_pension_employees', 'salary_data_2026_11_active_employees', 'salary_data_2026_11_a_grade_employees', 'salary_data_2026_11', 'salary_data_2026_04_retired_employees', 'salary_data_2026_04_pension_employees', 'salary_data_2026_04_active_employees', 'salary_data_2026_04_a_grade_employees', 'salary_data_2026_05_retired_employees', 'salary_data_2026_05_pension_employees', 'salary_data_2026_05_active_employees', 'salary_data_2026_05_a_grade_employees', 'salary_data_2026_06_retired_employees', 'salary_data_2026_06_pension_employees', 'salary_data_2026_06_active_employees', 'salary_data_2026_06_a_grade_employees', 'salary_data_2026_07_retired_employees', 'salary_data_2026_07_pension_employees', 'salary_data_2026_07_active_employees', 'salary_data_2026_07_a_grade_employees', 'salary_data_2026_08_retired_employees', 'salary_data_2026_08_pension_employees', 'salary_data_2026_08_active_employees', 'salary_data_2026_08_a_grade_employees'], 'error_details': [], 'success': True}
2025-06-26 17:51:13.911 | INFO     | src.gui.dialogs:_setup_field_mapping:1033 | 开始加载Excel列信息: C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls, 工作表: 全部在职人员工资表
2025-06-26 17:51:14.015 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 23列, 10行
2025-06-26 17:51:14.016 | INFO     | src.gui.dialogs:_setup_field_mapping:1045 | 成功加载Excel列: 23 个列
2025-06-26 17:51:14.020 | INFO     | src.gui.dialogs:_update_excel_columns_in_combos:1085 | 成功更新下拉框Excel列选项: 23 个列
2025-06-26 17:51:14.021 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行0, 列工号, 错误: 1
2025-06-26 17:51:14.022 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行0, 列工号, 错误: 1
2025-06-26 17:51:14.022 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-26 17:51:14.023 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-26 17:51:14.023 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-26 17:51:14.024 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-26 17:51:14.025 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行4, 列2025年岗位工资, 错误: 6
2025-06-26 17:51:14.029 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行4, 列2025年岗位工资, 错误: 6
2025-06-26 17:51:14.029 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行6, 列2025年岗位工资, 错误: 6
2025-06-26 17:51:14.030 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行6, 列2025年岗位工资, 错误: 6
2025-06-26 17:51:14.031 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行7, 列2025年薪级工资, 错误: 7
2025-06-26 17:51:14.031 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行7, 列2025年薪级工资, 错误: 7
2025-06-26 17:51:14.032 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行9, 列津贴, 错误: 8
2025-06-26 17:51:14.032 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行9, 列津贴, 错误: 8
2025-06-26 17:51:14.034 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行11, 列2025年奖励性绩效预发, 错误: 17
2025-06-26 17:51:14.034 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行11, 列2025年奖励性绩效预发, 错误: 17
2025-06-26 17:51:14.035 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行12, 列应发工资, 错误: 20
2025-06-26 17:51:14.036 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行12, 列应发工资, 错误: 20
2025-06-26 17:51:14.036 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行14, 列2025公积金, 错误: 21
2025-06-26 17:51:14.037 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行14, 列2025公积金, 错误: 21
2025-06-26 17:51:14.039 | INFO     | src.gui.dialogs:_auto_map_fields:1172 | 智能映射完成: 10 个字段
2025-06-26 17:51:33.303 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行0, 列工号, 错误: 1
2025-06-26 17:51:33.303 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行1, 列姓名, 错误: 2
2025-06-26 17:51:33.304 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行3, 列部门名称, 错误: 3
2025-06-26 17:51:33.305 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行4, 列2025年岗位工资, 错误: 6
2025-06-26 17:51:33.305 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行6, 列2025年岗位工资, 错误: 6
2025-06-26 17:51:33.306 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行7, 列2025年薪级工资, 错误: 7
2025-06-26 17:51:33.306 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行9, 列津贴, 错误: 8
2025-06-26 17:51:33.307 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行11, 列2025年奖励性绩效预发, 错误: 17
2025-06-26 17:51:33.307 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行12, 列应发工资, 错误: 20
2025-06-26 17:51:33.308 | ERROR    | src.gui.dialogs:_update_preview_for_row:1250 | 更新预览失败: 行14, 列2025公积金, 错误: 21
2025-06-26 17:51:33.308 | INFO     | src.gui.dialogs:_auto_map_fields:1172 | 智能映射完成: 10 个字段
2025-06-26 17:52:14.559 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 17:52:14.560 | ERROR    | src.gui.dialogs:_show_settings:2490 | 显示设置对话框失败: name 'QWidget' is not defined
2025-06-26 17:52:23.471 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 17:52:23.472 | ERROR    | src.gui.dialogs:_show_settings:2490 | 显示设置对话框失败: name 'QWidget' is not defined
2025-06-26 17:52:27.693 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:52:27.883 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:52:27.898 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:52:27.898 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:52:27.899 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:52:28.099 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:52:28.100 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 17:52:28.101 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:52:28.101 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:52:28.102 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:52:28.200 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-26 17:52:28.202 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:52:28.204 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-26 17:52:28.204 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-26 17:52:28.205 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-26 17:52:28.205 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 17:52:28.206 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 12 个字段
2025-06-26 17:52:28.218 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_10_retired_employees
2025-06-26 17:52:28.218 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_10_retired_employees 生成标准化字段映射: 12 个字段
2025-06-26 17:52:28.219 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-26 17:52:28.223 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-26 17:52:28.224 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-26 17:52:28.226 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_10_retired_employees 不存在，将根据模板创建...
2025-06-26 17:52:28.241 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。
2025-06-26 17:52:28.242 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:52:28.242 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:52:28.242 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:52:28.349 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-26 17:52:28.352 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:52:28.353 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-26 17:52:28.353 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-26 17:52:28.354 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-26 17:52:28.354 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 17:52:28.355 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 21 个字段
2025-06-26 17:52:28.367 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_10_pension_employees
2025-06-26 17:52:28.367 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_10_pension_employees 生成标准化字段映射: 21 个字段
2025-06-26 17:52:28.368 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-26 17:52:28.372 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-26 17:52:28.373 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:52:28.374 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_10_pension_employees 不存在，将根据模板创建...
2025-06-26 17:52:28.394 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。
2025-06-26 17:52:28.395 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:52:28.396 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:52:28.396 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:52:28.527 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-26 17:52:28.531 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:52:28.531 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-26 17:52:28.534 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-26 17:52:28.535 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-26 17:52:28.536 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-26 17:52:28.537 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 23 个字段
2025-06-26 17:52:28.550 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_10_active_employees
2025-06-26 17:52:28.551 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_10_active_employees 生成标准化字段映射: 23 个字段
2025-06-26 17:52:28.551 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-26 17:52:28.557 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-26 17:52:28.558 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:52:28.559 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_10_active_employees 不存在，将根据模板创建...
2025-06-26 17:52:28.589 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。
2025-06-26 17:52:28.591 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 17:52:28.591 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 17:52:28.592 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 17:52:28.696 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-26 17:52:28.698 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 17:52:28.699 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-26 17:52:28.700 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-26 17:52:28.700 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-26 17:52:28.701 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-26 17:52:28.702 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 17 个字段
2025-06-26 17:52:28.713 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_10_a_grade_employees
2025-06-26 17:52:28.713 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_10_a_grade_employees 生成标准化字段映射: 17 个字段
2025-06-26 17:52:28.714 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet A岗职工 存在 2 个验证错误
2025-06-26 17:52:28.718 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet A岗职工 数据处理完成: 62 行
2025-06-26 17:52:28.719 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 17:52:28.720 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_10_a_grade_employees 不存在，将根据模板创建...
2025-06-26 17:52:28.735 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。
2025-06-26 17:52:28.736 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_10_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_10_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_10_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_10_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-26 17:52:28.740 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_10_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_10_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_10_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_10_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-10', 'data_description': '2025年10月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 10月 > 全部在职人员'}
2025-06-26 17:52:28.743 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_10_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_10_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_10_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_10_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_10_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_10_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-10', 'data_description': '2025年10月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 10月 > 全部在职人员'}
2025-06-26 17:52:28.745 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 10月 > 全部在职人员'
2025-06-26 17:52:28.750 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:52:28.750 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: ['工资表', '2025年', '10月', '全部在职人员']
2025-06-26 17:52:28.750 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷新导航面板
2025-06-26 17:52:28.751 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-26 17:52:28.751 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 17:52:28.752 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 17:52:28.797 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 88 个匹配类型 'salary_data' 的表
2025-06-26 17:52:28.799 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 17:52:28.801 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 4 个月份
2025-06-26 17:52:28.802 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 17:52:28.803 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 17:52:28.804 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 17:52:28.805 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 17:52:28.806 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 17:52:28.807 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 17:52:28.812 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 17:52:28.812 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 17:52:28.814 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 17:52:28.815 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 22 个月份
2025-06-26 17:52:28.815 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 17:52:28.816 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 17:52:28.816 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:52:29.617 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:52:29.628 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 5月', '异动人员表 > 2025年 > 4月']
2025-06-26 17:52:29.628 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:52:29.629 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:52:29.630 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 17:52:29.630 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2025_10_active_employees，第1页，每页50条
2025-06-26 17:52:29.631 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2025_10_active_employees 第1页
2025-06-26 17:52:29.632 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:52:29.635 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2025_10_active_employees 第1页数据，每页50条
2025-06-26 17:52:29.636 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_10_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:52:29.639 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工资表 > 2025年 > 10月 > 全部在职人员
2025-06-26 17:52:29.647 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_10_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 17:52:29.647 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:52:29.650 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:52:29.651 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 17:52:29.662 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 17:52:29.667 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_10_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:52:29.668 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:52:29.670 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_10_active_employees 无字段偏好设置，显示所有字段
2025-06-26 17:52:29.671 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:52:29.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 17:52:29.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:52:29.676 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_10_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:52:29.679 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:52:29.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:52:29.707 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 34.44ms
2025-06-26 17:52:29.707 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:52:29.708 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 17:52:50.820 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:52:50.821 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:52:50.821 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:52:50.823 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_10_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:52:50.825 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_10_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 17:52:50.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:52:50.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:52:50.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:52:50.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:52:50.908 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:52:50.909 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:52:50.909 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:52:50.910 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:52:50.910 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:52:50.911 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:52:51.326 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:52:51.334 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:52:51.359 | INFO     | src.modules.data_import.config_sync_manager:update_single_field_mapping:309 | 字段映射更新成功: salary_data_2026_08_active_employees.employee_id -> 工号_测试修改
2025-06-26 17:52:51.376 | INFO     | src.modules.data_import.config_sync_manager:update_single_field_mapping:309 | 字段映射更新成功: salary_data_2026_08_active_employees.employee_id -> 工号
2025-06-26 17:52:56.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5390.33ms
2025-06-26 17:52:56.218 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 17:52:56.218 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:53:07.402 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 10月 > 全部在职人员', '工资表', '异动人员表', '异动人员表 > 2025年 > 5月']
2025-06-26 17:53:07.403 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 10月 > A岗职工
2025-06-26 17:53:07.404 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:53:07.405 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 17:53:07.405 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2025_10_a_grade_employees，第1页，每页50条
2025-06-26 17:53:07.406 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2025_10_a_grade_employees 第1页
2025-06-26 17:53:07.406 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 10月 > A岗职工
2025-06-26 17:53:07.407 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2025_10_a_grade_employees 第1页数据，每页50条
2025-06-26 17:53:07.408 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_10_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 17:53:07.415 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_10_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 17:53:07.415 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:53:07.418 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 17:53:07.419 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 17:53:07.421 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 17:53:07.427 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_10_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:53:07.428 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:53:07.436 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_10_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-26 17:53:07.437 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 17:53:07.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 17:53:07.443 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_10_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:53:07.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 17:53:07.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:53:07.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 17:53:12.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5413.39ms
2025-06-26 17:53:12.855 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 17:53:12.855 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 17:53:23.044 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 17:53:23.045 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:53:23.046 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 17:53:23.046 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_10_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 17:53:23.048 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_10_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 17:53:24.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 12
2025-06-26 17:53:24.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(12)自动调整最大可见行数为: 12
2025-06-26 17:53:24.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:53:24.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 12 行, 16 列
2025-06-26 17:53:24.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 12 行, 最大可见行数: 12, 耗时: 1371.90ms
2025-06-26 17:53:24.422 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示12条记录，字段数: 16
2025-06-26 17:53:24.422 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 17:53:28.882 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 10月 > 全部在职人员', '工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 5月']
2025-06-26 17:53:28.883 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 10月 > 退休人员
2025-06-26 17:53:28.883 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:53:28.884 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 17:53:28.885 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 10月 > 退休人员
2025-06-26 17:53:28.885 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:53:28.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_10_pension_employees 获取数据...
2025-06-26 17:53:28.889 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_10_pension_employees 获取 13 行数据。
2025-06-26 17:53:28.890 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 17:53:28.894 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 4 个字段重命名
2025-06-26 17:53:28.895 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '津贴', 'deduction', '应发工资', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:53:28.899 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:53:28.902 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_10_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 17:53:28.904 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 17:53:28.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 12 -> 13
2025-06-26 17:53:28.908 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 17:53:28.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:53:28.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 17:53:29.012 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 105.07ms
2025-06-26 17:53:29.014 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 17:53:37.011 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 10月 > 全部在职人员', '工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年 > 5月']
2025-06-26 17:53:37.012 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 10月 > 离休人员
2025-06-26 17:53:37.012 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 17:53:37.013 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 17:53:37.014 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 10月 > 离休人员
2025-06-26 17:53:37.014 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 17:53:37.015 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_10_retired_employees 获取数据...
2025-06-26 17:53:37.017 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_10_retired_employees 获取 2 行数据。
2025-06-26 17:53:37.018 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 17:53:37.021 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 2 个字段重命名
2025-06-26 17:53:37.029 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 17:53:37.029 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:53:37.032 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_10_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 17:53:37.032 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 17:53:37.033 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 17:53:37.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 2
2025-06-26 17:53:37.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 17:53:37.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 17:53:37.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 17:53:37.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 87.05ms
2025-06-26 17:53:37.122 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 17:53:46.375 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:53:46.376 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:53:46.377 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 17:53:46.377 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:53:46.379 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 17:53:46.390 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: False
2025-06-26 17:53:46.393 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3245 | 已注册 4 个表格到表头管理器
2025-06-26 17:53:46.405 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 17:53:46.407 | INFO     | src.gui.prototype.prototype_main_window:hide_menu_bar:1819 | 菜单栏已隐藏
2025-06-26 17:53:46.407 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 17:53:46.910 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2442871749040 已经注册，将覆盖现有注册
2025-06-26 17:53:46.911 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2442871741840 已经注册，将覆盖现有注册
2025-06-26 17:53:46.912 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2442902202720 已经注册，将覆盖现有注册
2025-06-26 17:53:46.912 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2442902250864 已经注册，将覆盖现有注册
2025-06-26 17:53:46.913 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3245 | 已注册 4 个表格到表头管理器
2025-06-26 17:53:47.696 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:53:47.697 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:53:47.698 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 17:53:47.698 | INFO     | src.modules.system_config.user_preferences:save_preferences:251 | 正在保存偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 17:53:47.701 | INFO     | src.modules.system_config.user_preferences:save_preferences:255 | 偏好设置保存成功
2025-06-26 17:53:47.718 | INFO     | src.gui.prototype.prototype_main_window:_on_menu_visibility_changed:2226 | 菜单栏可见性变化: True
2025-06-26 17:53:47.723 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2442871749040 已经注册，将覆盖现有注册
2025-06-26 17:53:47.724 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2442871741840 已经注册，将覆盖现有注册
2025-06-26 17:53:47.724 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2442902202720 已经注册，将覆盖现有注册
2025-06-26 17:53:47.725 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2442902250864 已经注册，将覆盖现有注册
2025-06-26 17:53:47.725 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3245 | 已注册 4 个表格到表头管理器
2025-06-26 17:53:47.736 | INFO     | src.gui.prototype.prototype_main_window:refresh_layout:2286 | 窗口布局全面刷新完成
2025-06-26 17:53:47.737 | INFO     | src.gui.prototype.prototype_main_window:show_menu_bar:1792 | 菜单栏已显示
2025-06-26 17:53:47.737 | INFO     | src.gui.prototype.prototype_main_window:_on_settings_clicked:2210 | 设置按钮点击，切换菜单栏显示状态
2025-06-26 17:53:48.241 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2442871749040 已经注册，将覆盖现有注册
2025-06-26 17:53:48.242 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2442871741840 已经注册，将覆盖现有注册
2025-06-26 17:53:48.242 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2442902202720 已经注册，将覆盖现有注册
2025-06-26 17:53:48.243 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2442902250864 已经注册，将覆盖现有注册
2025-06-26 17:53:48.243 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3245 | 已注册 4 个表格到表头管理器
2025-06-26 17:53:49.972 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 17:54:30.737 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:54:30.737 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:54:30.738 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:54:30.738 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:54:30.739 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:54:30.739 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:54:30.739 | INFO     | __main__:migrate_all_mappings:196 | 开始字段映射迁移...
2025-06-26 17:54:30.742 | INFO     | __main__:migrate_all_mappings:202 | 已备份原配置文件到: state/data/field_mappings.json.backup_20250626_175430
2025-06-26 17:54:30.745 | INFO     | __main__:migrate_single_table:152 | 表 异动人员表 需要迁移，当前格式: mixed
2025-06-26 17:54:30.746 | INFO     | __main__:migrate_all_mappings:234 | 表 异动人员表 迁移完成
2025-06-26 17:54:30.747 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2025_05_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.748 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.748 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2025_05_active_employees 迁移完成
2025-06-26 17:54:30.748 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2018_06_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.749 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本
离休费' 的标准映射，使用原字段名: 基本
离休费
2025-06-26 17:54:30.749 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余
津贴' 的标准映射，使用原字段名: 结余
津贴
2025-06-26 17:54:30.750 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '生活
补贴' 的标准映射，使用原字段名: 生活
补贴
2025-06-26 17:54:30.750 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房
补贴' 的标准映射，使用原字段名: 住房
补贴
2025-06-26 17:54:30.750 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业
补贴' 的标准映射，使用原字段名: 物业
补贴
2025-06-26 17:54:30.751 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离休
补贴' 的标准映射，使用原字段名: 离休
补贴
2025-06-26 17:54:30.752 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.753 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.754 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2018_06_retired_employees 迁移完成
2025-06-26 17:54:30.754 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2018_06_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.755 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.755 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.756 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离退休生活补贴' 的标准映射，使用原字段名: 离退休生活补贴
2025-06-26 17:54:30.756 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.757 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.758 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.758 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.758 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.759 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.759 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.760 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.765 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.765 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.765 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.768 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2018_06_pension_employees 迁移完成
2025-06-26 17:54:30.768 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2018_06_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.769 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.770 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年薪级工资' 的标准映射，使用原字段名: 2025年薪级工资
2025-06-26 17:54:30.770 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.770 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.770 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.771 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '交通补贴' 的标准映射，使用原字段名: 交通补贴
2025-06-26 17:54:30.771 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.772 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.772 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.774 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '通讯补贴' 的标准映射，使用原字段名: 通讯补贴
2025-06-26 17:54:30.774 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.775 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.776 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.777 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.777 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2018_06_active_employees 迁移完成
2025-06-26 17:54:30.777 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2018_06_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.778 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.778 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄工资' 的标准映射，使用原字段名: 2025年校龄工资
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年生活补贴' 的标准映射，使用原字段名: 2025年生活补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.779 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2018_06_a_grade_employees 迁移完成
2025-06-26 17:54:30.779 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2017_01_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本
离休费' 的标准映射，使用原字段名: 基本
离休费
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余
津贴' 的标准映射，使用原字段名: 结余
津贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '生活
补贴' 的标准映射，使用原字段名: 生活
补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房
补贴' 的标准映射，使用原字段名: 住房
补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业
补贴' 的标准映射，使用原字段名: 物业
补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离休
补贴' 的标准映射，使用原字段名: 离休
补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.779 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2017_01_retired_employees 迁移完成
2025-06-26 17:54:30.779 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2017_01_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离退休生活补贴' 的标准映射，使用原字段名: 离退休生活补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.779 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.795 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2017_01_pension_employees 迁移完成
2025-06-26 17:54:30.795 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2017_01_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年薪级工资' 的标准映射，使用原字段名: 2025年薪级工资
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '交通补贴' 的标准映射，使用原字段名: 交通补贴
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.795 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.810 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '通讯补贴' 的标准映射，使用原字段名: 通讯补贴
2025-06-26 17:54:30.810 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.810 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.810 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.810 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.810 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2017_01_active_employees 迁移完成
2025-06-26 17:54:30.819 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2017_01_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.819 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.820 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄工资' 的标准映射，使用原字段名: 2025年校龄工资
2025-06-26 17:54:30.820 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.821 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.823 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.824 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年生活补贴' 的标准映射，使用原字段名: 2025年生活补贴
2025-06-26 17:54:30.826 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.827 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.828 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2017_01_a_grade_employees 迁移完成
2025-06-26 17:54:30.828 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_02_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.830 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本
离休费' 的标准映射，使用原字段名: 基本
离休费
2025-06-26 17:54:30.834 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余
津贴' 的标准映射，使用原字段名: 结余
津贴
2025-06-26 17:54:30.834 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '生活
补贴' 的标准映射，使用原字段名: 生活
补贴
2025-06-26 17:54:30.834 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房
补贴' 的标准映射，使用原字段名: 住房
补贴
2025-06-26 17:54:30.834 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业
补贴' 的标准映射，使用原字段名: 物业
补贴
2025-06-26 17:54:30.834 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离休
补贴' 的标准映射，使用原字段名: 离休
补贴
2025-06-26 17:54:30.834 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.837 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.839 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_02_retired_employees 迁移完成
2025-06-26 17:54:30.840 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_02_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.840 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.841 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.843 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离退休生活补贴' 的标准映射，使用原字段名: 离退休生活补贴
2025-06-26 17:54:30.843 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.846 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.848 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.849 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.849 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.849 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.852 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.852 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.853 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.853 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.853 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.853 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_02_pension_employees 迁移完成
2025-06-26 17:54:30.855 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_02_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.855 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.856 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年薪级工资' 的标准映射，使用原字段名: 2025年薪级工资
2025-06-26 17:54:30.856 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.856 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.856 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.857 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '交通补贴' 的标准映射，使用原字段名: 交通补贴
2025-06-26 17:54:30.857 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.857 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.860 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.861 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '通讯补贴' 的标准映射，使用原字段名: 通讯补贴
2025-06-26 17:54:30.861 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.862 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.863 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.864 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.865 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_02_active_employees 迁移完成
2025-06-26 17:54:30.865 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_02_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.866 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.866 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄工资' 的标准映射，使用原字段名: 2025年校龄工资
2025-06-26 17:54:30.866 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.867 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.867 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.867 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年生活补贴' 的标准映射，使用原字段名: 2025年生活补贴
2025-06-26 17:54:30.868 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.868 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.869 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_02_a_grade_employees 迁移完成
2025-06-26 17:54:30.871 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_11_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.872 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本
离休费' 的标准映射，使用原字段名: 基本
离休费
2025-06-26 17:54:30.874 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余
津贴' 的标准映射，使用原字段名: 结余
津贴
2025-06-26 17:54:30.874 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '生活
补贴' 的标准映射，使用原字段名: 生活
补贴
2025-06-26 17:54:30.874 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房
补贴' 的标准映射，使用原字段名: 住房
补贴
2025-06-26 17:54:30.875 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业
补贴' 的标准映射，使用原字段名: 物业
补贴
2025-06-26 17:54:30.880 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离休
补贴' 的标准映射，使用原字段名: 离休
补贴
2025-06-26 17:54:30.881 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.881 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.881 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_11_retired_employees 迁移完成
2025-06-26 17:54:30.881 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_11_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.884 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.884 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.885 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离退休生活补贴' 的标准映射，使用原字段名: 离退休生活补贴
2025-06-26 17:54:30.885 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.885 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.886 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.886 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.887 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.887 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.888 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.888 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.888 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.889 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.890 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.891 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_11_pension_employees 迁移完成
2025-06-26 17:54:30.894 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_11_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.894 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.894 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年薪级工资' 的标准映射，使用原字段名: 2025年薪级工资
2025-06-26 17:54:30.895 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.895 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.896 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.896 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '交通补贴' 的标准映射，使用原字段名: 交通补贴
2025-06-26 17:54:30.896 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:30.897 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:30.897 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.897 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '通讯补贴' 的标准映射，使用原字段名: 通讯补贴
2025-06-26 17:54:30.898 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.899 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.899 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.899 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.901 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_11_active_employees 迁移完成
2025-06-26 17:54:30.902 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_11_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.903 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:30.904 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄工资' 的标准映射，使用原字段名: 2025年校龄工资
2025-06-26 17:54:30.908 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:30.909 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:30.910 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.910 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年生活补贴' 的标准映射，使用原字段名: 2025年生活补贴
2025-06-26 17:54:30.910 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:30.913 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.913 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_11_a_grade_employees 迁移完成
2025-06-26 17:54:30.914 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_11 需要迁移，当前格式: mixed
2025-06-26 17:54:30.914 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2026年岗位工资' 的标准映射，使用原字段名: 2026年岗位工资
2025-06-26 17:54:30.915 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_11 迁移完成
2025-06-26 17:54:30.915 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_04_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.915 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本离休费' 的标准映射，使用原字段名: 基本离休费
2025-06-26 17:54:30.916 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.916 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.917 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_04_retired_employees 迁移完成
2025-06-26 17:54:30.917 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_04_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.917 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.918 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.918 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.918 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.919 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.920 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.924 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.924 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.924 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.924 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.925 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_04_pension_employees 迁移完成
2025-06-26 17:54:30.925 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_04_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.926 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.926 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.927 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.927 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.927 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.928 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_04_active_employees 迁移完成
2025-06-26 17:54:30.928 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_04_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.929 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄salary' 的标准映射，使用原字段名: 2025年校龄salary
2025-06-26 17:54:30.929 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.930 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.937 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_04_a_grade_employees 迁移完成
2025-06-26 17:54:30.937 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_05_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.938 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本离休费' 的标准映射，使用原字段名: 基本离休费
2025-06-26 17:54:30.938 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.938 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.938 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_05_retired_employees 迁移完成
2025-06-26 17:54:30.938 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_05_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.941 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.942 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.942 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.942 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.942 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.943 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.943 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.944 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.944 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.944 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.945 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_05_pension_employees 迁移完成
2025-06-26 17:54:30.946 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_05_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.947 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.947 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.947 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.948 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.948 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.949 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_05_active_employees 迁移完成
2025-06-26 17:54:30.950 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_05_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.950 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄salary' 的标准映射，使用原字段名: 2025年校龄salary
2025-06-26 17:54:30.951 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.953 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.954 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_05_a_grade_employees 迁移完成
2025-06-26 17:54:30.954 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_06_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.955 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本离休费' 的标准映射，使用原字段名: 基本离休费
2025-06-26 17:54:30.955 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.957 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.957 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_06_retired_employees 迁移完成
2025-06-26 17:54:30.957 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_06_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.958 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.958 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.959 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.959 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.960 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.960 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.961 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.962 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.966 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.969 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.969 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_06_pension_employees 迁移完成
2025-06-26 17:54:30.969 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_06_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.969 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.972 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.973 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.973 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.973 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.973 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_06_active_employees 迁移完成
2025-06-26 17:54:30.974 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_06_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.974 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄salary' 的标准映射，使用原字段名: 2025年校龄salary
2025-06-26 17:54:30.975 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.975 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.975 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_06_a_grade_employees 迁移完成
2025-06-26 17:54:30.976 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_07_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.976 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本离休费' 的标准映射，使用原字段名: 基本离休费
2025-06-26 17:54:30.976 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.977 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.977 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_07_retired_employees 迁移完成
2025-06-26 17:54:30.979 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_07_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.980 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:30.980 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:30.980 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:30.982 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:30.982 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:30.982 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:30.983 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:30.983 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:30.984 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:30.984 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:30.985 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_07_pension_employees 迁移完成
2025-06-26 17:54:30.985 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_07_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.986 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.986 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:30.986 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:30.987 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.987 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:30.988 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_07_active_employees 迁移完成
2025-06-26 17:54:30.988 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_07_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.989 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄salary' 的标准映射，使用原字段名: 2025年校龄salary
2025-06-26 17:54:30.990 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:30.990 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:30.991 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_07_a_grade_employees 迁移完成
2025-06-26 17:54:30.991 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_08_retired_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:30.992 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本
离休费' 的标准映射，使用原字段名: 基本
离休费
2025-06-26 17:54:30.996 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余
津贴' 的标准映射，使用原字段名: 结余
津贴
2025-06-26 17:54:30.996 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '生活
补贴' 的标准映射，使用原字段名: 生活
补贴
2025-06-26 17:54:30.997 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房
补贴' 的标准映射，使用原字段名: 住房
补贴
2025-06-26 17:54:30.998 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业
补贴' 的标准映射，使用原字段名: 物业
补贴
2025-06-26 17:54:30.998 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离休
补贴' 的标准映射，使用原字段名: 离休
补贴
2025-06-26 17:54:30.998 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:30.998 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:30.999 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_08_retired_employees 迁移完成
2025-06-26 17:54:31.000 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_08_pension_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:31.001 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:31.001 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:31.002 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离退休生活补贴' 的标准映射，使用原字段名: 离退休生活补贴
2025-06-26 17:54:31.002 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:31.003 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:31.004 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:31.005 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:31.006 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:31.006 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:31.007 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:31.007 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:31.008 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:31.008 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:31.008 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:31.008 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_08_pension_employees 迁移完成
2025-06-26 17:54:31.009 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_08_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:31.011 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:31.013 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年薪级工资' 的标准映射，使用原字段名: 2025年薪级工资
2025-06-26 17:54:31.015 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:31.016 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:31.017 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:31.017 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '交通补贴' 的标准映射，使用原字段名: 交通补贴
2025-06-26 17:54:31.017 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:31.017 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:31.017 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:31.017 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '通讯补贴' 的标准映射，使用原字段名: 通讯补贴
2025-06-26 17:54:31.020 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:31.021 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:31.022 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:31.022 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:31.025 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_08_active_employees 迁移完成
2025-06-26 17:54:31.025 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2026_08_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:31.026 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:31.026 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄工资' 的标准映射，使用原字段名: 2025年校龄工资
2025-06-26 17:54:31.027 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:31.027 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:31.028 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:31.029 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年生活补贴' 的标准映射，使用原字段名: 2025年生活补贴
2025-06-26 17:54:31.029 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:31.029 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:31.030 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2026_08_a_grade_employees 迁移完成
2025-06-26 17:54:31.032 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2025_10_retired_employees 需要迁移，当前格式: excel_column
2025-06-26 17:54:31.032 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本
离休费' 的标准映射，使用原字段名: 基本
离休费
2025-06-26 17:54:31.033 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余
津贴' 的标准映射，使用原字段名: 结余
津贴
2025-06-26 17:54:31.033 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '生活
补贴' 的标准映射，使用原字段名: 生活
补贴
2025-06-26 17:54:31.034 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房
补贴' 的标准映射，使用原字段名: 住房
补贴
2025-06-26 17:54:31.037 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业
补贴' 的标准映射，使用原字段名: 物业
补贴
2025-06-26 17:54:31.038 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离休
补贴' 的标准映射，使用原字段名: 离休
补贴
2025-06-26 17:54:31.040 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '护理费' 的标准映射，使用原字段名: 护理费
2025-06-26 17:54:31.041 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '合计' 的标准映射，使用原字段名: 合计
2025-06-26 17:54:31.041 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2025_10_retired_employees 迁移完成
2025-06-26 17:54:31.041 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2025_10_pension_employees 需要迁移，当前格式: excel_column
2025-06-26 17:54:31.044 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '基本退休费' 的标准映射，使用原字段名: 基本退休费
2025-06-26 17:54:31.044 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:31.045 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '离退休生活补贴' 的标准映射，使用原字段名: 离退休生活补贴
2025-06-26 17:54:31.045 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:31.045 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:31.046 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '增资预付' 的标准映射，使用原字段名: 增资预付
2025-06-26 17:54:31.046 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2016待遇调整' 的标准映射，使用原字段名: 2016待遇调整
2025-06-26 17:54:31.047 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2017待遇调整' 的标准映射，使用原字段名: 2017待遇调整
2025-06-26 17:54:31.047 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2018待遇调整' 的标准映射，使用原字段名: 2018待遇调整
2025-06-26 17:54:31.047 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2019待遇调整' 的标准映射，使用原字段名: 2019待遇调整
2025-06-26 17:54:31.048 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2020待遇调整' 的标准映射，使用原字段名: 2020待遇调整
2025-06-26 17:54:31.048 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2021待遇调整' 的标准映射，使用原字段名: 2021待遇调整
2025-06-26 17:54:31.049 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2022待遇调整' 的标准映射，使用原字段名: 2022待遇调整
2025-06-26 17:54:31.049 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2023待遇调整' 的标准映射，使用原字段名: 2023待遇调整
2025-06-26 17:54:31.049 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2025_10_pension_employees 迁移完成
2025-06-26 17:54:31.050 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2025_10_active_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:31.050 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:31.051 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年薪级工资' 的标准映射，使用原字段名: 2025年薪级工资
2025-06-26 17:54:31.052 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:31.053 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:31.053 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:31.053 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '交通补贴' 的标准映射，使用原字段名: 交通补贴
2025-06-26 17:54:31.054 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '物业补贴' 的标准映射，使用原字段名: 物业补贴
2025-06-26 17:54:31.054 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '住房补贴' 的标准映射，使用原字段名: 住房补贴
2025-06-26 17:54:31.055 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '车补' 的标准映射，使用原字段名: 车补
2025-06-26 17:54:31.059 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '通讯补贴' 的标准映射，使用原字段名: 通讯补贴
2025-06-26 17:54:31.059 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:31.060 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '补发' 的标准映射，使用原字段名: 补发
2025-06-26 17:54:31.060 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:31.061 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '代扣代存养老保险' 的标准映射，使用原字段名: 代扣代存养老保险
2025-06-26 17:54:31.062 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2025_10_active_employees 迁移完成
2025-06-26 17:54:31.062 | INFO     | __main__:migrate_single_table:152 | 表 salary_data_2025_10_a_grade_employees 需要迁移，当前格式: mixed
2025-06-26 17:54:31.062 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年岗位工资' 的标准映射，使用原字段名: 2025年岗位工资
2025-06-26 17:54:31.063 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年校龄工资' 的标准映射，使用原字段名: 2025年校龄工资
2025-06-26 17:54:31.064 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '结余津贴' 的标准映射，使用原字段名: 结余津贴
2025-06-26 17:54:31.064 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年基础性绩效' 的标准映射，使用原字段名: 2025年基础性绩效
2025-06-26 17:54:31.064 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '卫生费' 的标准映射，使用原字段名: 卫生费
2025-06-26 17:54:31.065 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年生活补贴' 的标准映射，使用原字段名: 2025年生活补贴
2025-06-26 17:54:31.065 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '2025年奖励性绩效预发' 的标准映射，使用原字段名: 2025年奖励性绩效预发
2025-06-26 17:54:31.067 | WARNING  | __main__:convert_excel_to_db_mapping:125 | 未找到 '借支' 的标准映射，使用原字段名: 借支
2025-06-26 17:54:31.067 | INFO     | __main__:migrate_all_mappings:234 | 表 salary_data_2025_10_a_grade_employees 迁移完成
2025-06-26 17:54:31.084 | INFO     | __main__:migrate_all_mappings:259 | 字段映射迁移完成: {'total_tables': 66, 'migrated_tables': 43, 'skipped_tables': 23, 'error_tables': 0, 'migrated_table_names': ['异动人员表', 'salary_data_2025_05_active_employees', 'salary_data_2018_06_retired_employees', 'salary_data_2018_06_pension_employees', 'salary_data_2018_06_active_employees', 'salary_data_2018_06_a_grade_employees', 'salary_data_2017_01_retired_employees', 'salary_data_2017_01_pension_employees', 'salary_data_2017_01_active_employees', 'salary_data_2017_01_a_grade_employees', 'salary_data_2026_02_retired_employees', 'salary_data_2026_02_pension_employees', 'salary_data_2026_02_active_employees', 'salary_data_2026_02_a_grade_employees', 'salary_data_2026_11_retired_employees', 'salary_data_2026_11_pension_employees', 'salary_data_2026_11_active_employees', 'salary_data_2026_11_a_grade_employees', 'salary_data_2026_11', 'salary_data_2026_04_retired_employees', 'salary_data_2026_04_pension_employees', 'salary_data_2026_04_active_employees', 'salary_data_2026_04_a_grade_employees', 'salary_data_2026_05_retired_employees', 'salary_data_2026_05_pension_employees', 'salary_data_2026_05_active_employees', 'salary_data_2026_05_a_grade_employees', 'salary_data_2026_06_retired_employees', 'salary_data_2026_06_pension_employees', 'salary_data_2026_06_active_employees', 'salary_data_2026_06_a_grade_employees', 'salary_data_2026_07_retired_employees', 'salary_data_2026_07_pension_employees', 'salary_data_2026_07_active_employees', 'salary_data_2026_07_a_grade_employees', 'salary_data_2026_08_retired_employees', 'salary_data_2026_08_pension_employees', 'salary_data_2026_08_active_employees', 'salary_data_2026_08_a_grade_employees', 'salary_data_2025_10_retired_employees', 'salary_data_2025_10_pension_employees', 'salary_data_2025_10_active_employees', 'salary_data_2025_10_a_grade_employees'], 'error_details': [], 'success': True}
2025-06-26 17:55:10.727 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 17:55:10.727 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 17:55:10.727 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 17:55:10.728 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 17:55:10.728 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 17:55:10.729 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 17:55:11.037 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:55:11.046 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 17:55:11.062 | INFO     | src.modules.data_import.config_sync_manager:update_single_field_mapping:309 | 字段映射更新成功: salary_data_2026_08_active_employees.employee_id -> 工号_测试修改
2025-06-26 17:55:11.078 | INFO     | src.modules.data_import.config_sync_manager:update_single_field_mapping:309 | 字段映射更新成功: salary_data_2026_08_active_employees.employee_id -> 工号
2025-06-26 18:07:58.900 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 18:07:58.901 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 18:07:58.901 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 18:07:58.902 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 18:07:58.902 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 18:07:58.902 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 18:08:00.135 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 18:08:00.135 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 18:08:00.135 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 18:08:00.135 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 18:08:00.135 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 18:08:00.135 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 18:08:00.135 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 18:08:00.151 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 18:08:00.151 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 18:08:00.151 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 18:08:00.151 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 18:08:00.151 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 18:08:00.151 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 18:08:00.151 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 18:08:00.151 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 18:08:00.420 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1493 | 菜单栏创建完成
2025-06-26 18:08:00.420 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 18:08:00.420 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 18:08:00.420 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 18:08:00.435 | INFO     | src.gui.prototype.prototype_main_window:__init__:1469 | 菜单栏管理器初始化完成
2025-06-26 18:08:00.435 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 18:08:00.435 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2170 | 管理器设置完成，包含增强版表头管理器
2025-06-26 18:08:00.435 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 18:08:00.435 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 18:08:00.487 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 18:08:00.488 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 18:08:00.505 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:916 | 开始从元数据动态加载工资数据...
2025-06-26 18:08:00.509 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:923 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 18:08:00.521 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:654 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 18:08:00.533 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:689 | 恢复导航状态: 100个展开项
2025-06-26 18:08:00.539 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年 > 12月', '工资表', '异动人员表', '异动人员表 > 2025年']
2025-06-26 18:08:00.540 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:495 | 增强导航面板初始化完成
2025-06-26 18:08:00.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 18:08:00.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 18:08:00.617 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:08:00.618 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 18:08:00.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 18:08:00.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:00.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:00.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 9.56ms
2025-06-26 18:08:00.627 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:00.636 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 18:08:00.685 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 18:08:00.724 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 18:08:00.724 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 18:08:00.725 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2143 | 快捷键设置完成
2025-06-26 18:08:00.725 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2132 | 主窗口UI设置完成。
2025-06-26 18:08:00.726 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2198 | 信号连接设置完成
2025-06-26 18:08:00.727 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:08:00.870 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2627 | 已加载字段映射信息，共66个表的映射
2025-06-26 18:08:00.871 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 18:08:00.871 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:00.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:00.875 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.10ms
2025-06-26 18:08:00.875 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:00.876 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:00.876 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 18:08:00.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:00.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:00.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 1.50ms
2025-06-26 18:08:00.879 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:00.879 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:00.880 | INFO     | src.gui.prototype.prototype_main_window:__init__:2084 | 原型主窗口初始化完成
2025-06-26 18:08:01.255 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 18:08:01.260 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 18:08:01.261 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 18:08:01.326 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:938 | 执行延迟的工资数据加载...
2025-06-26 18:08:01.327 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 18:08:01.328 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 18:08:01.389 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 88 个匹配类型 'salary_data' 的表
2025-06-26 18:08:01.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 18:08:01.395 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 4 个月份
2025-06-26 18:08:01.399 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 18:08:01.400 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 18:08:01.409 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 18:08:01.410 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 18:08:01.411 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 18:08:01.412 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 18:08:01.412 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 18:08:01.413 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 18:08:01.414 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 18:08:01.415 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 22 个月份
2025-06-26 18:08:01.415 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:08:01.416 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表', '异动人员表', '异动人员表 > 2025年', '异动人员表 > 2025年 > 5月']
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年
2025-06-26 18:08:08.859 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:08.859 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:08.859 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:08.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 18:08:08.875 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:08.875 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:08.875 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年
2025-06-26 18:08:10.596 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年', '工资表', '异动人员表', '异动人员表 > 2025年']
2025-06-26 18:08:10.596 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 1月
2025-06-26 18:08:10.596 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 18:08:10.596 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 15.17ms
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:10.611 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:10.611 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 0.00ms
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 18:08:10.611 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:10.611 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月
2025-06-26 18:08:11.787 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 18:08:11.787 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 18:08:11.787 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(62条)，启用分页模式
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_01_a_grade_employees，第1页，每页50条
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_01_a_grade_employees 第1页
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > A岗职工
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_a_grade_employees 第1页数据，每页50条
2025-06-26 18:08:11.802 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-26 18:08:11.802 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2640 | 表 salary_data_2026_01_a_grade_employees 没有字段映射配置
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-26 18:08:11.802 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-26 18:08:11.818 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-26 18:08:11.818 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:08:11.818 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2687 | 应用表 salary_data_2026_01_a_grade_employees 的字段偏好: 7个字段 ['id', 'employee_id', 'employee_name', 'id_card', 'department', 'position', 'basic_salary']
2025-06-26 18:08:11.818 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 18:08:11.818 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-26 18:08:11.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 18:08:11.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 18:08:11.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 7 列
2025-06-26 18:08:11.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 49.34ms
2025-06-26 18:08:11.869 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 18:08:11.870 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-26 18:08:14.177 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-26 18:08:14.177 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 18:08:24.911 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 退休人员
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 18:08:24.911 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2026_01_pension_employees 获取数据...
2025-06-26 18:08:24.911 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2026_01_pension_employees 获取 13 行数据。
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 18:08:24.911 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 18:08:24.927 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', 'department', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 18:08:24.927 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:08:24.927 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_01_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 18:08:24.927 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 18:08:25.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 13
2025-06-26 18:08:25.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 18:08:25.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:08:25.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 18:08:25.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 318.96ms
2025-06-26 18:08:25.247 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:08:35.081 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2026_01_active_employees 第1页
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:08:35.081 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_01_active_employees 第1页数据，每页50条
2025-06-26 18:08:35.081 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 18:08:35.081 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 18:08:35.097 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 18:08:35.097 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 1 个字段重命名
2025-06-26 18:08:35.097 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 18:08:35.097 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 18:08:35.097 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 18:08:35.097 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:08:35.112 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 18:08:35.112 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 18:08:35.112 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 18:08:35.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 13 -> 50
2025-06-26 18:08:35.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 18:08:35.129 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:08:35.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 18:08:35.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 34.22ms
2025-06-26 18:08:35.150 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 18:08:35.154 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 18:08:41.441 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 18:08:41.441 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:08:41.441 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 18:08:41.441 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_01_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 18:08:41.441 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_01_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 18:08:41.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 18:08:41.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 18:08:41.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:08:41.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 18:08:46.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5471.08ms
2025-06-26 18:08:46.928 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 18:08:46.928 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 18:09:04.567 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-26 18:09:04.567 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2026年 > 1月 > 全部在职人员。打开导入对话框。
2025-06-26 18:09:04.567 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-26 18:09:04.567 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:09:04.567 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-26 18:09:04.567 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-06-26 18:09:04.583 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:09:04.616 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 88 个匹配类型 'salary_data' 的表
2025-06-26 18:09:04.666 | INFO     | src.gui.dialogs:_init_field_mapping:1925 | 初始化字段映射表格: 17 个默认字段
2025-06-26 18:09:04.775 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 18:09:04.776 | INFO     | src.gui.dialogs:_apply_default_settings:2141 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-06-26 18:09:04.777 | INFO     | src.gui.dialogs:_setup_tooltips:2396 | 工具提示设置完成
2025-06-26 18:09:04.778 | INFO     | src.gui.dialogs:_setup_shortcuts:2435 | 快捷键设置完成
2025-06-26 18:09:04.778 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-26 18:09:09.490 | INFO     | src.gui.dialogs:_on_target_changed:2080 | 目标位置已更新: 工资表 > 2025年 > 1月 > 全部在职人员
2025-06-26 18:09:11.699 | INFO     | src.gui.dialogs:_on_target_changed:2080 | 目标位置已更新: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:18.266 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 18:09:19.449 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 18:09:19.449 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-26 18:09:19.452 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2176 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-26 18:09:29.419 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 23列, 20行
2025-06-26 18:09:35.763 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-26 18:09:35.763 | ERROR    | src.gui.dialogs:_show_settings:2490 | 显示设置对话框失败: name 'QWidget' is not defined
2025-06-26 18:09:39.332 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 18:09:39.541 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 18:09:39.541 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 18:09:39.541 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 18:09:39.556 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 18:09:39.763 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 18:09:39.763 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-26 18:09:39.763 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 18:09:39.763 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 18:09:39.763 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 18:09:39.882 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-26 18:09:39.884 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 18:09:39.885 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-26 18:09:39.886 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-26 18:09:39.887 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-26 18:09:39.887 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 18:09:39.888 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 12 个字段
2025-06-26 18:09:39.908 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_12_retired_employees
2025-06-26 18:09:39.914 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_12_retired_employees 生成标准化字段映射: 12 个字段
2025-06-26 18:09:39.918 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-26 18:09:39.925 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-26 18:09:39.928 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-26 18:09:39.929 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_12_retired_employees 不存在，将根据模板创建...
2025-06-26 18:09:39.944 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_12_retired_employees 保存 2 条数据。
2025-06-26 18:09:39.945 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 18:09:39.946 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 18:09:39.946 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 18:09:40.079 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-26 18:09:40.081 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 18:09:40.082 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-26 18:09:40.082 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-26 18:09:40.083 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-26 18:09:40.083 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-26 18:09:40.084 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 21 个字段
2025-06-26 18:09:40.086 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_12_pension_employees
2025-06-26 18:09:40.086 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_12_pension_employees 生成标准化字段映射: 21 个字段
2025-06-26 18:09:40.086 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-26 18:09:40.086 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-26 18:09:40.086 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 18:09:40.102 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_12_pension_employees 不存在，将根据模板创建...
2025-06-26 18:09:40.102 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_12_pension_employees 保存 13 条数据。
2025-06-26 18:09:40.102 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 18:09:40.102 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 18:09:40.102 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 18:09:40.261 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-26 18:09:40.261 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 18:09:40.261 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-26 18:09:40.261 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-26 18:09:40.261 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-26 18:09:40.261 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-26 18:09:40.261 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 23 个字段
2025-06-26 18:09:40.277 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_12_active_employees
2025-06-26 18:09:40.277 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_12_active_employees 生成标准化字段映射: 23 个字段
2025-06-26 18:09:40.277 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-26 18:09:40.292 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-26 18:09:40.292 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 18:09:40.292 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_12_active_employees 不存在，将根据模板创建...
2025-06-26 18:09:40.340 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_12_active_employees 保存 1396 条数据。
2025-06-26 18:09:40.344 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-26 18:09:40.344 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-26 18:09:40.348 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-26 18:09:40.482 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-26 18:09:40.485 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-26 18:09:40.485 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-26 18:09:40.486 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-26 18:09:40.487 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-26 18:09:40.488 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:680 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-26 18:09:40.488 | INFO     | src.modules.data_import.auto_field_mapping_generator:create_initial_field_mapping:660 | 智能字段映射生成完成: 17 个字段
2025-06-26 18:09:40.501 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:349 | 完整字段映射保存成功: salary_data_2025_12_a_grade_employees
2025-06-26 18:09:40.502 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:458 | 为表 salary_data_2025_12_a_grade_employees 生成标准化字段映射: 17 个字段
2025-06-26 18:09:40.502 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:468 | Sheet A岗职工 存在 2 个验证错误
2025-06-26 18:09:40.507 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:477 | Sheet A岗职工 数据处理完成: 62 行
2025-06-26 18:09:40.508 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-26 18:09:40.510 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2025_12_a_grade_employees 不存在，将根据模板创建...
2025-06-26 18:09:40.510 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2025_12_a_grade_employees 保存 62 条数据。
2025-06-26 18:09:40.510 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_12_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_12_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-26 18:09:40.510 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_12_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_12_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-12', 'data_description': '2025年12月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 12月 > 全部在职人员'}
2025-06-26 18:09:40.510 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2443 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_12_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_12_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_12_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_12_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_12_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_12_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-12', 'data_description': '2025年12月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 12月 > 全部在职人员'}
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2454 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 12月 > 全部在职人员'
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2462 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2520 | 检查是否需要更新导航面板: ['工资表', '2025年', '12月', '全部在职人员']
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2524 | 检测到工资数据导入，开始刷新导航面板
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2528 | 使用强制刷新方法
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1036 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 18:09:40.526 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1044 | 找到工资表节点: 📊 工资表
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:09:40.542 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2026_01_active_employees，第1页，每页50条
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2785 | 缓存命中: salary_data_2026_01_active_employees 第1页
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2026_01_active_employees 无需字段重命名
2025-06-26 18:09:40.542 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-26 18:09:40.542 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:09:40.557 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2026_01_active_employees 无字段偏好设置，显示所有字段
2025-06-26 18:09:40.559 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 18:09:40.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 18:09:40.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 18:09:40.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:09:40.576 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 18:09:46.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 5720.70ms
2025-06-26 18:09:46.283 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 18:09:46.285 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 18:09:46.290 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:09:46.332 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 18:09:46.336 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 18:09:46.336 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2025年，包含 5 个月份
2025-06-26 18:09:46.345 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 18:09:46.351 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 18:09:46.352 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 18:09:46.353 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 18:09:46.353 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 18:09:46.354 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 18:09:46.354 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 18:09:46.355 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 18:09:46.355 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1129 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 18:09:46.356 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1149 | 工资数据导航已强制刷新: 11 个年份, 23 个月份
2025-06-26 18:09:46.356 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1152 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 18:09:46.357 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1191 | force_refresh_salary_data 执行完成
2025-06-26 18:09:46.357 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2533 | 将在800ms后导航到: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:47.159 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2566 | 尝试导航到新导入的路径: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:47.170 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表', '异动人员表']
2025-06-26 18:09:47.171 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:47.172 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:09:47.172 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2748 | 数据量大(1396条)，启用分页模式
2025-06-26 18:09:47.173 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2776 | 使用分页模式加载 salary_data_2025_12_active_employees，第1页，每页50条
2025-06-26 18:09:47.173 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2819 | 缓存未命中，从数据库加载: salary_data_2025_12_active_employees 第1页
2025-06-26 18:09:47.174 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:47.175 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2025_12_active_employees 第1页数据，每页50条
2025-06-26 18:09:47.177 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_12_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 18:09:47.180 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2571 | 已成功导航到新导入的路径: 工资表 > 2025年 > 12月 > 全部在职人员
2025-06-26 18:09:47.195 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_12_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 18:09:47.203 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 18:09:47.207 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 5 个字段重命名
2025-06-26 18:09:47.207 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 18:09:47.218 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2849 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 18:09:47.239 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_12_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 18:09:47.239 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:09:47.249 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_12_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 18:09:47.253 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_12_active_employees 无字段偏好设置，显示所有字段
2025-06-26 18:09:47.270 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 18:09:47.272 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 18:09:47.272 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 18:09:47.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:09:47.275 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 18:09:47.323 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 51.10ms
2025-06-26 18:09:47.326 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 18:09:47.327 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 18:10:03.901 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-26 18:10:03.916 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:10:03.916 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 18:10:03.916 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2025_12_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 18:10:03.916 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2025_12_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 18:10:03.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 50
2025-06-26 18:10:03.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 18:10:03.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:10:03.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 18:10:08.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 4911.92ms
2025-06-26 18:10:08.828 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-26 18:10:08.828 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表 > 2025年 > 12月 > 全部在职人员', '工资表']
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 12月 > 离休人员
2025-06-26 18:10:20.502 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(2条)，使用普通模式
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 12月 > 离休人员
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 18:10:20.502 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_12_retired_employees 获取数据...
2025-06-26 18:10:20.502 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_12_retired_employees 获取 2 行数据。
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 2 行数据，16 个字段
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 2 个字段重命名
2025-06-26 18:10:20.502 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', 'allowance', 'deduction', 'total_salary', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 18:10:20.502 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:10:20.523 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_12_retired_employees 无字段偏好设置，显示所有字段
2025-06-26 18:10:20.523 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-26 18:10:20.523 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2583 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-26 18:10:20.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 50 -> 2
2025-06-26 18:10:20.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-26 18:10:20.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:10:20.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-26 18:10:20.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 229.74ms
2025-06-26 18:10:20.753 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-26 18:10:24.882 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 12月 > 全部在职人员', '工资表 > 2026年 > 1月', '工资表 > 2026年', '工资表']
2025-06-26 18:10:24.882 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2703 | 导航变化: 工资表 > 2025年 > 12月 > 退休人员
2025-06-26 18:10:24.882 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 18:10:24.882 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2751 | 数据量小(13条)，使用普通模式
2025-06-26 18:10:24.882 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:785 | 导航选择: 工资表 > 2025年 > 12月 > 退休人员
2025-06-26 18:10:24.882 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2910 | 统一加载所有字段（修复字段不一致问题）
2025-06-26 18:10:24.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2025_12_pension_employees 获取数据...
2025-06-26 18:10:24.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2025_12_pension_employees 获取 13 行数据。
2025-06-26 18:10:24.882 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2917 | 成功从数据库加载 13 行数据，16 个字段
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2653 | 字段映射应用成功: 4 个字段重命名
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.prototype_main_window:_load_database_data_with_mapping:2922 | 应用字段映射后的表头: ['id', 'employee_id', '姓名', 'id_card', '部门名称', 'position', 'basic_salary', 'performance_bonus', 'overtime_pay', '津贴', 'deduction', '应发工资', 'month', 'year', 'created_at', 'updated_at']
2025-06-26 18:10:24.898 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2693 | 表 salary_data_2025_12_pension_employees 无字段偏好设置，显示所有字段
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 2 -> 13
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 18:10:24.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-26 18:10:24.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 16.83ms
2025-06-26 18:10:24.915 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-26 18:10:33.498 | INFO     | __main__:main:302 | 应用程序正常退出
