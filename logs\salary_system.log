2025-06-26 19:43:23.313 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 19:43:23.313 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 19:43:23.313 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 19:43:23.314 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 19:43:23.314 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 19:43:23.316 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 19:43:23.926 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 19:43:23.926 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-26 19:43:23.926 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2025年 > 06月 > 全部在职人员
2025-06-26 19:43:23.928 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 19:43:23.932 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 19:43:23.933 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 19:43:23.934 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 19:43:23.935 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 19:43:23.935 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 19:43:23.945 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 19:43:23.945 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 19:43:23.946 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 19:43:23.946 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-26 19:43:23.994 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 19:43:23.994 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 19:43:24.036 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 19:56:20.859 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 19:56:20.859 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 19:56:20.859 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 19:56:20.859 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 19:56:20.859 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 19:56:20.859 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 19:56:22.204 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 19:56:22.204 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 19:56:22.204 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 19:56:22.204 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 19:56:22.204 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 19:56:22.204 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 19:56:22.204 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 19:56:22.219 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 19:56:22.219 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 19:56:22.219 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 19:56:22.219 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 19:56:22.219 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 19:56:22.235 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 19:56:22.235 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 19:56:22.235 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 19:56:22.567 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1510 | 菜单栏创建完成
2025-06-26 19:56:22.567 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 19:56:22.567 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 19:56:22.567 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 19:56:22.567 | INFO     | src.gui.prototype.prototype_main_window:__init__:1486 | 菜单栏管理器初始化完成
2025-06-26 19:56:22.567 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 19:56:22.567 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2187 | 管理器设置完成，包含增强版表头管理器
2025-06-26 19:56:22.582 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 19:56:22.582 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 19:56:22.648 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 19:56:22.650 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 19:56:22.653 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1031 | 开始从元数据动态加载工资数据...
2025-06-26 19:56:22.653 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1038 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 19:56:22.670 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:676 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 19:56:22.728 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:711 | 恢复导航状态: 102个展开项
2025-06-26 19:56:22.783 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['异动人员表', '工资表 > 2025年', '工资表', '异动人员表 > 2025年 > 5月', '异动人员表 > 2025年 > 4月']
2025-06-26 19:56:22.785 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:517 | 增强导航面板初始化完成
2025-06-26 19:56:22.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 19:56:22.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 19:56:22.866 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 19:56:22.866 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 19:56:22.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 19:56:22.881 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 19:56:22.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 19:56:22.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 33.97ms
2025-06-26 19:56:22.917 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 19:56:22.931 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 19:56:23.018 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 19:56:23.030 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 19:56:23.031 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 19:56:23.031 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2160 | 快捷键设置完成
2025-06-26 19:56:23.032 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2149 | 主窗口UI设置完成。
2025-06-26 19:56:23.032 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2215 | 信号连接设置完成
2025-06-26 19:56:23.033 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 19:56:23.201 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2644 | 已加载字段映射信息，共70个表的映射
2025-06-26 19:56:23.201 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 19:56:23.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 19:56:23.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 19:56:23.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.09ms
2025-06-26 19:56:23.205 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 19:56:23.206 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 19:56:23.206 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 19:56:23.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 19:56:23.209 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 19:56:23.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.12ms
2025-06-26 19:56:23.213 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 19:56:23.214 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 19:56:23.214 | INFO     | src.gui.prototype.prototype_main_window:__init__:2101 | 原型主窗口初始化完成
2025-06-26 19:56:23.615 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 19:56:23.620 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:999 | 执行延迟的自动选择最新数据...
2025-06-26 19:56:23.621 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:939 | 开始自动选择最新数据...
2025-06-26 19:56:23.621 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-26 19:56:23.676 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 19:56:23.733 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 19:56:23.734 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:947 | 获取到最新数据路径: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 19:56:23.735 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:973 | 已自动选择最新数据: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 19:56:23.738 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 19:56:23.739 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2732 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-26 19:56:23.750 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 19:56:23.792 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2775 | 数据量大(1396条)，启用分页模式
2025-06-26 19:56:23.804 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2803 | 使用分页模式加载 salary_data_2026_11_active_employees，第1页，每页50条
2025-06-26 19:56:23.808 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2846 | 缓存未命中，从数据库加载: salary_data_2026_11_active_employees 第1页
2025-06-26 19:56:23.809 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1010 | 延迟自动选择最新数据成功
2025-06-26 19:56:23.811 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_11_active_employees 第1页数据，每页50条
2025-06-26 19:56:23.812 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 19:56:23.813 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_11_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 19:56:23.814 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 19:56:23.823 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_11_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 19:56:23.828 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 19:56:23.834 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1053 | 执行延迟的工资数据加载...
2025-06-26 19:56:23.835 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 7 个字段重命名
2025-06-26 19:56:23.835 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1151 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 19:56:23.836 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 19:56:23.856 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1159 | 找到工资表节点: 📊 工资表
2025-06-26 19:56:23.912 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 19:56:23.918 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 19:56:23.922 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2025年，包含 5 个月份
2025-06-26 19:56:23.925 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 19:56:23.926 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 19:56:23.931 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 19:56:23.932 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 19:56:23.934 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 19:56:23.935 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 19:56:23.935 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 19:56:23.936 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 19:56:23.937 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 19:56:23.938 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1264 | 工资数据导航已强制刷新: 11 个年份, 23 个月份
2025-06-26 19:56:23.939 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1267 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 19:56:23.942 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1306 | force_refresh_salary_data 执行完成
2025-06-26 19:56:23.983 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2876 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 19:56:24.111 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_11_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 19:56:24.112 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 19:56:24.115 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2026_11_active_employees 无字段偏好设置，显示所有字段
2025-06-26 19:56:24.115 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 19:56:24.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 19:56:24.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 19:56:24.124 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_11_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 19:56:24.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 19:56:24.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 19:56:24.148 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 33.11ms
2025-06-26 19:56:24.149 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 19:56:24.149 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 19:57:50.688 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-26 20:01:37.535 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-26 20:01:37.551 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-26 20:01:37.551 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-26 20:01:37.551 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-26 20:01:37.552 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-26 20:01:37.553 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-26 20:01:38.795 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-26 20:01:38.795 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-26 20:01:38.795 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 20:01:38.811 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 20:01:38.811 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-26 20:01:38.811 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-26 20:01:38.811 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-26 20:01:38.811 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-26 20:01:38.811 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-26 20:01:38.811 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 20:01:38.811 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-26 20:01:38.811 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-26 20:01:38.811 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-26 20:01:38.811 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-26 20:01:38.811 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-26 20:01:39.127 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1510 | 菜单栏创建完成
2025-06-26 20:01:39.127 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 20:01:39.127 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 20:01:39.127 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-26 20:01:39.127 | INFO     | src.gui.prototype.prototype_main_window:__init__:1486 | 菜单栏管理器初始化完成
2025-06-26 20:01:39.127 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-26 20:01:39.127 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2187 | 管理器设置完成，包含增强版表头管理器
2025-06-26 20:01:39.143 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-26 20:01:39.144 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-26 20:01:39.195 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:_load_data:556 | 数据加载成功: state/user/tree_expansion_data.json
2025-06-26 20:01:39.202 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-26 20:01:39.218 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1031 | 开始从元数据动态加载工资数据...
2025-06-26 20:01:39.218 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1038 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-26 20:01:39.224 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:676 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-26 20:01:39.236 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:711 | 恢复导航状态: 102个展开项
2025-06-26 20:01:39.290 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2024年', '异动人员表 > 2024年', '工资表', '异动人员表']
2025-06-26 20:01:39.293 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:517 | 增强导航面板初始化完成
2025-06-26 20:01:39.356 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-26 20:01:39.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-26 20:01:39.370 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 20:01:39.371 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-26 20:01:39.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-26 20:01:39.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 20:01:39.378 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 20:01:39.381 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 6.81ms
2025-06-26 20:01:39.382 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 20:01:39.432 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-26 20:01:39.536 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-26 20:01:39.547 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-26 20:01:39.547 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-26 20:01:39.548 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2160 | 快捷键设置完成
2025-06-26 20:01:39.548 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2149 | 主窗口UI设置完成。
2025-06-26 20:01:39.549 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2215 | 信号连接设置完成
2025-06-26 20:01:39.550 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 20:01:39.764 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2644 | 已加载字段映射信息，共70个表的映射
2025-06-26 20:01:39.765 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 20:01:39.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 20:01:39.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 20:01:39.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.11ms
2025-06-26 20:01:39.769 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 20:01:39.770 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 20:01:39.770 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-26 20:01:39.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2621 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-26 20:01:39.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-26 20:01:39.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.05ms
2025-06-26 20:01:39.774 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-26 20:01:39.776 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 20:01:39.776 | INFO     | src.gui.prototype.prototype_main_window:__init__:2101 | 原型主窗口初始化完成
2025-06-26 20:01:40.220 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-26 20:01:40.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:999 | 执行延迟的自动选择最新数据...
2025-06-26 20:01:40.305 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:939 | 开始自动选择最新数据...
2025-06-26 20:01:40.310 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-26 20:01:40.365 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 20:01:40.388 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 20:01:40.391 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:947 | 获取到最新数据路径: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 20:01:40.392 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:973 | 已自动选择最新数据: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 20:01:40.394 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2026年 > 11月 > 全部在职人员
2025-06-26 20:01:40.395 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2732 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-26 20:01:40.397 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-26 20:01:40.399 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2775 | 数据量大(1396条)，启用分页模式
2025-06-26 20:01:40.429 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2803 | 使用分页模式加载 salary_data_2026_11_active_employees，第1页，每页50条
2025-06-26 20:01:40.451 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2846 | 缓存未命中，从数据库加载: salary_data_2026_11_active_employees 第1页
2025-06-26 20:01:40.452 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1010 | 延迟自动选择最新数据成功
2025-06-26 20:01:40.454 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-26 20:01:40.458 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-26 20:01:40.462 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2026_11_active_employees 第1页数据，每页50条
2025-06-26 20:01:40.466 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1053 | 执行延迟的工资数据加载...
2025-06-26 20:01:40.467 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_11_active_employees 分页获取数据: 第1页, 每页50条
2025-06-26 20:01:40.468 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1151 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-26 20:01:40.469 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1159 | 找到工资表节点: 📊 工资表
2025-06-26 20:01:40.478 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_11_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-26 20:01:40.501 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-26 20:01:40.519 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 92 个匹配类型 'salary_data' 的表
2025-06-26 20:01:40.523 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 7 个字段重命名
2025-06-26 20:01:40.552 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2026年，包含 9 个月份
2025-06-26 20:01:40.553 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-26 20:01:40.555 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2025年，包含 5 个月份
2025-06-26 20:01:40.556 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2024年，包含 1 个月份
2025-06-26 20:01:40.557 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2023年，包含 1 个月份
2025-06-26 20:01:40.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2022年，包含 1 个月份
2025-06-26 20:01:40.558 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2021年，包含 1 个月份
2025-06-26 20:01:40.559 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2020年，包含 1 个月份
2025-06-26 20:01:40.562 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2019年，包含 1 个月份
2025-06-26 20:01:40.563 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2018年，包含 1 个月份
2025-06-26 20:01:40.564 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2017年，包含 1 个月份
2025-06-26 20:01:40.565 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1244 | 创建年份节点: 2016年，包含 1 个月份
2025-06-26 20:01:40.566 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1264 | 工资数据导航已强制刷新: 11 个年份, 23 个月份
2025-06-26 20:01:40.566 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1267 | 默认路径: 工资表 > 2026年 > 1月 > 全部在职人员
2025-06-26 20:01:40.567 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1306 | force_refresh_salary_data 执行完成
2025-06-26 20:01:40.616 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2876 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-26 20:01:40.686 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2026_11_active_employees 分页获取数据: 第2页, 每页50条
2025-06-26 20:01:40.686 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-26 20:01:40.694 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2026_11_active_employees 无字段偏好设置，显示所有字段
2025-06-26 20:01:40.695 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-26 20:01:40.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2592 | 最大可见行数已更新: 1000 -> 50
2025-06-26 20:01:40.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2645 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-26 20:01:40.702 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2026_11_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-26 20:01:40.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2034 | 表头映射应用完成: 16 个表头
2025-06-26 20:01:40.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-26 20:01:40.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:1999 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 105.56ms
2025-06-26 20:01:40.806 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-26 20:01:40.807 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-26 20:03:05.596 | INFO     | __main__:main:302 | 应用程序正常退出
